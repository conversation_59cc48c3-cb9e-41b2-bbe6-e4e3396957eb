<?php

namespace Database\Factories;

use App\Models\Company;
use App\Models\DocumentDefault;
use App\Models\User;
use App\Enums\DocumentType;
use App\Enums\Template;
use App\Enums\Font;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\DocumentDefault>
 */
class DocumentDefaultFactory extends Factory
{
    protected $model = DocumentDefault::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $type = $this->faker->randomElement(DocumentType::cases());

        return [
            'company_id' => null, // Sera fourni par le seeder
            'type' => $type,
            'logo' => null,
            'qr_code' => null,
            'show_logo' => true,
            'show_qr_code' => false,
            'number_prefix' => $type->Prefix(),
            'header' => $type->Header(),
            'subheader' => 'Document commercial',
            'client_name_key' => ['fr' => 'Client', 'en' => 'Client'],
            'client_register_key' => ['fr' => 'N° Client', 'en' => 'Client No.'],
            'client_address_key' => ['fr' => 'Adresse', 'en' => 'Address'],
            'object_name_key' => ['fr' => 'Objet', 'en' => 'Subject'],
            'show_header' => true,
            'show_client_details' => true,
            'show_objet_details' => true,
            'show_company_details' => true,
            'show_lines' => true,
            'show_totals' => true,
            'show_discount' => true,
            'show_tax' => true,
            'show_payment_details' => $type !== DocumentType::Estimate,
            'show_notes' => true,
            'show_signature' => false,
            'show_footer' => true,
            'template' => Template::Default ,
            'font' => Font::Inter,
            'created_by' => null, // Sera fourni par le seeder
            'updated_by' => fn(array $attributes) => $attributes['created_by'],
        ];
    }

    /**
     * Indicate that this is for invoices.
     */
    public function invoice(): static
    {
        return $this->state(fn(array $attributes) => [
            'type' => DocumentType::Invoice,
            'number_prefix' => DocumentType::Invoice->Prefix(),
            'header' => DocumentType::Invoice->Header(),
            'show_payment_details' => true,
        ]);
    }

    /**
     * Indicate that this is for estimates.
     */
    public function estimate(): static
    {
        return $this->state(fn(array $attributes) => [
            'type' => DocumentType::Estimate,
            'number_prefix' => DocumentType::Estimate->Prefix(),
            'header' => DocumentType::Estimate->Header(),
            'show_payment_details' => false,
        ]);
    }

    /**
     * Indicate that this is for delivery notes.
     */
    public function delivery(): static
    {
        return $this->state(fn(array $attributes) => [
            'type' => DocumentType::Delivery,
            'number_prefix' => DocumentType::Delivery->Prefix(),
            'header' => DocumentType::Delivery->Header(),
            'show_payment_details' => false,
            'show_totals' => false,
        ]);
    }

    /**
     * Indicate that this is for bills.
     */
    public function bill(): static
    {
        return $this->state(fn(array $attributes) => [
            'type' => DocumentType::Bill,
            'number_prefix' => DocumentType::Bill->Prefix(),
            'header' => DocumentType::Bill->Header(),
            'show_payment_details' => true,
        ]);
    }
}
