<?php

use Illuminate\Database\Migrations\Migration;

use Illuminate\Database\Schema\Blueprint;

use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('document_defaults', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained()->onDelete('cascade');
            $table->string('type');
            $table->string('logo')->nullable();
            $table->json('qr_code')->nullable();
            $table->text('qr_options')->nullable();
            $table->boolean('show_logo')->default(false);
            $table->boolean('show_qr_code')->default(false);
            $table->string('number_prefix')->nullable();
            $table->string('header')->nullable();
            $table->string('subheader')->nullable();
            $table->json('client_name')->nullable();
            $table->json('client_register')->nullable();
            $table->json('client_address')->nullable();
            $table->json('object_name')->nullable();
            $table->json('object_value')->nullable();
            $table->text('footer')->nullable();
            $table->string('accent_color')->nullable();
            $table->string('font')->nullable();
            $table->string('template')->nullable();
            $table->string('currency_code')->nullable();
            $table->text('notes')->nullable();
            $table->text('text_with_amount_in_words')->nullable();
            $table->boolean('show_header')->default(true);
            $table->boolean('show_client_details')->default(true);
            $table->boolean('show_objet_details')->default(true);
            $table->boolean('show_company_details')->default(true);
            $table->boolean('show_lines')->default(true);
            $table->boolean('show_totals')->default(true);
            $table->boolean('show_discount')->default(true);
            $table->boolean('show_tax')->default(true);
            $table->boolean('show_payment_details')->default(true);
            $table->boolean('show_notes')->default(true);
            $table->boolean('show_signature')->default(true);
            $table->boolean('show_footer')->default(true);
            $table->string('discount_method')->default('per_document');
            $table->json('final_amount')->nullable()->default(0);
            $table->string('authorized_personnel_signature')->nullable();
            $table->json('authorized_personnel_signature_name')->nullable();
            $table->json('authorized_personnel_signature_role')->nullable();
            $table->json('payment')->nullable();
            $table->json('item_name')->nullable();
            $table->json('unit_name')->nullable();
            $table->json('price_name')->nullable();
            $table->json('amount_name')->nullable();
            $table->json('delivery_person')->nullable();
            $table->json('receiver_person')->nullable();
            $table->json('remarks')->nullable();
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->unique(['company_id', 'type']);
        });
    }
    public function down(): void
    {
        Schema::dropIfExists('document_defaults');
    }
};
