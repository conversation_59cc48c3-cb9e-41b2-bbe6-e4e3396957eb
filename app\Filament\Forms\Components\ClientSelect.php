<?php

namespace App\Filament\Forms\Components;

use App\Filament\Company\Resources\ClientResource;
use App\Models\Client;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Support\Enums\MaxWidth;

class ClientSelect extends Select
{
    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->searchable()
            ->preload()
            ->createOptionForm(fn(Form $form) => $this->createClientForm($form))
            ->createOptionAction(fn(Action $action) => $this->createClientAction($action));

        $this->relationship('client', 'name');

        $this->createOptionUsing(static function (array $data) {
            $client = Client::create($data);
            return $client->getKey();
        });
    }

    protected function createClientForm(Form $form): Form
    {
        return ClientResource::form($form);
    }

    protected function createClientAction(Action $action): Action
    {
        return $action
            ->label('Create client')
            ->slideOver()
            ->modalWidth(MaxWidth::ThreeExtraLarge)
            ->modalHeading('Create a new client');
    }
}
