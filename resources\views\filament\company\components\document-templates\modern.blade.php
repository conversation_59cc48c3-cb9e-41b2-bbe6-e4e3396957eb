<x-company.document-template.container class="modern-template-container" :preview="$preview">
    <!-- Colored Header with Logo -->
    @if ($document->showHeader)
        <x-company.document-template.header class="bg-gray-800 h-24">
            <!-- Logo -->
            <div class="w-2/3">
                @if ($document->logo && $document->showLogo)
                    <x-company.document-template.logo class="ml-8" :src="$document->logo" />
                @endif
            </div>

            <!-- Ribbon Container -->
            <div class="w-1/3 absolute right-0 top-0 p-3 h-32 flex flex-col justify-end rounded-bl-sm"
                style="background: {{ $document->accentColor }};">
                @if ($document->header)
                    <h1 class="text-4xl font-bold text-white text-center uppercase">{{ $document->header }}</h1>
                @endif
            </div>
        </x-company.document-template.header>
    @endif

    <!-- Company Details -->
    <x-company.document-template.metadata class="modern-template-metadata space-y-8">
        @if ($document->showCompanyDetails)
            <div class="text-sm">
                <strong class="text-sm block">{{ $document->company->name }}</strong>
                @if ($formattedAddress = $document->company->getFormattedAddressHtml())
                    {!! $formattedAddress !!}
                @endif
            </div>
        @endif

        <div class="flex justify-between items-end">
            <!-- Billing Details -->
            @if ($document->showClientDetails)
                <div class="text-sm">
                    <h3 class="text-gray-600 font-medium mb-1">BILL TO</h3>
                    <p class="text-sm font-bold" style="color: {{ $document->accentColor }}">
                        {{ $document->client_name ?? '' }} : {{ $document->client?->name ?? 'Client Not Found' }}</p>
                    <p class="text-sm font-bold">
                        {{ $document->client_register ?? '' }} :
                        {{ $document->client?->fiscal_number ?? 'Client Not Found' }}</p>
                    <p class="text-sm font-bold"> {{ $document->client_contact ?? '' }} :
                        {{ $document->client?->contact ?? 'Client Not Found' }}</p>

                    @if ($document->client && ($formattedAddress = $document->client->getFormattedAddressHtml()))
                        {!! $formattedAddress !!}
                    @endif
                </div>
            @endif

            @if ($document->showObjetDetails)
                <div class="text-sm">
                    <table class="min-w-full">
                        <tbody>
                            <tr>
                                <td class="font-semibold text-right pr-2">{{ $document->label->number }}:</td>
                                <td class="text-left pl-2">{{ $document->number }}</td>
                            </tr>
                            @if ($document->referenceNumber)
                                <tr>
                                    <td class="font-semibold text-right pr-2">{{ $document->label->referenceNumber }}:
                                    </td>
                                    <td class="text-left pl-2">{{ $document->referenceNumber }}</td>
                                </tr>
                            @endif
                            <tr>
                                <td class="font-semibold text-right pr-2">{{ $document->label->date }}:</td>
                                <td class="text-left pl-2">{{ $document->date }}</td>
                            </tr>
                            <tr>
                                <td class="font-semibold text-right pr-2">{{ $document->label->dueDate }}:</td>
                                <td class="text-left pl-2">{{ $document->dueDate }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            @endif
        </div>
    </x-company.document-template.metadata>

    <!-- Line Items Table -->
    @if ($document->showLines)
        <x-company.document-template.line-items class="modern-template-line-items">
            <table class="w-full text-left table-fixed">
                <thead class="text-sm leading-relaxed">
                    <tr class="text-gray-600">
                        @if ($document->lineItemColumns->isDelivery())
                            <th class="text-left pl-6 w-[30%] py-4">{{ $document->lineItemColumns->itemsLabel }}</th>
                            <th class="text-left w-[25%] py-4">{{ $document->lineItemColumns->deliveryLabel }}</th>
                            <th class="text-left w-[25%] py-4">{{ $document->lineItemColumns->receiverLabel }}</th>
                            <th class="text-left w-[20%] py-4">{{ $document->lineItemColumns->remarksLabel }}</th>
                        @else
                            <th class="text-left pl-6 w-[50%] py-4">{{ $document->lineItemColumns->itemsLabel }}</th>
                            <th class="text-center w-[10%] py-4">{{ $document->lineItemColumns->unitsLabel }}</th>
                            <th class="text-right w-[20%] py-4">{{ $document->lineItemColumns->priceLabel }}</th>
                            <th class="text-right pr-6 w-[20%] py-4">{{ $document->lineItemColumns->amountLabel }}</th>
                        @endif
                    </tr>
                </thead>
                <tbody class="text-sm border-y-2">
                    @foreach ($document->lineItems as $index => $item)
                        <tr @class(['bg-gray-100' => $index % 2 === 0])>
                            @if ($document->lineItemColumns->isDelivery())
                                <td class="text-left pl-6 font-semibold py-3">
                                    {{ $item->name }}
                                    @if ($item->description)
                                        <div class="text-gray-600 font-normal line-clamp-2 mt-1">
                                            {{ $item->description }}
                                        </div>
                                    @endif
                                </td>
                                <td class="text-left py-3">{{ $item->delivery ?? '-' }}</td>
                                <td class="text-left py-3">{{ $item->receiver ?? '-' }}</td>
                                <td class="text-left py-3">{{ $item->remarks ?? '-' }}</td>
                            @else
                                <td class="text-left pl-6 font-semibold py-3">
                                    {{ $item->name }}
                                    @if ($item->description)
                                        <div class="text-gray-600 font-normal line-clamp-2 mt-1">
                                            {{ $item->description }}
                                        </div>
                                    @endif
                                </td>
                                <td class="text-center py-3">{{ $item->quantity }}</td>
                                <td class="text-right py-3">{{ $item->unitPrice }}</td>
                                <td class="text-right pr-6 py-3">{{ $item->subtotal }}</td>
                            @endif
                        </tr>
                    @endforeach
                </tbody>
                @if (!$document->lineItemColumns->isDelivery() && $document->showTotals)
                    <tfoot class="text-sm summary-section">
                        @if ($document->subtotal)
                            <tr>
                                <td class="pl-6 py-2" colspan="2"></td>
                                <td class="text-right font-semibold py-2">Subtotal:</td>
                                <td class="text-right pr-6 py-2">{{ $document->subtotal }}</td>
                            </tr>
                        @endif
                        @if ($document->discount && $document->showDiscount)
                            <tr class="text-success-800">
                                <td class="pl-6 py-2" colspan="2"></td>
                                <td class="text-right py-2">Discount:</td>
                                <td class="text-right pr-6 py-2">
                                    ({{ $document->discount }})
                                </td>
                            </tr>
                        @endif
                        @if ($document->tax && $document->showTax)
                            <tr>
                                <td class="pl-6 py-2" colspan="2"></td>
                                <td class="text-right py-2">Tax:</td>
                                <td class="text-right pr-6 py-2">{{ $document->tax }}</td>
                            </tr>
                        @endif
                        <tr>
                            <td class="pl-6 py-2" colspan="2"></td>
                            <td class="text-right font-semibold border-t py-2">
                                {{ $document->amountDue ? 'Total' : 'Grand Total' }}:</td>
                            <td class="text-right border-t pr-6 py-2">{{ $document->total }}</td>
                        </tr>
                        @if ($document->amountDue)
                            <tr>
                                <td class="pl-6 py-2" colspan="2"></td>
                                <td class="text-right font-semibold border-t-4 border-double py-2">
                                    {{ $document->label->amountDue }}
                                    ({{ $document->currencyCode }}):
                                </td>
                                <td class="text-right border-t-4 border-double pr-6 py-2">{{ $document->amountDue }}
                                </td>
                            </tr>
                        @endif
                    </tfoot>
                @endif
            </table>

        </x-company.document-template.line-items>
    @endif

    @if (!$document->lineItemColumns->isDelivery() && $document->showPaymentDetails)
        <x-company.document-template.payment-breakdown :accentColor="$document->accentColor" :amountDue="$document->amountDue"
            headerTextColor="text-white" />
    @endif

    @if (!$document->lineItemColumns->isDelivery() && $document->showAmountInWords)
        <x-company.document-template.amount-in-words class="modern-template-amount-in-words pb-4">
            <h4 class="font-semibold px-6 text-sm">
                {{ $document->amountInWords->formattedText }}
            </h4>
        </x-company.document-template.amount-in-words>
    @endif

    @if (!$document->lineItemColumns->isDelivery() && $document->showSignature)
        <x-company.document-template.signature-section :document="$document" :showSignatureSection="true" :showRole="true"
            :showImage="true" :showName="true" imageWidth="w-20" imageHeight="h-12" />
    @endif

    <!-- Footer Notes -->
    @if ($document->showFooter)
        <x-company.document-template.footer class="modern-template-footer">
            @if ($document->showNotes)
                <h4 class="font-semibold px-6 text-sm" style="color: {{ $document->accentColor }}">
                    Terms & Conditions
                </h4>
                <span class="border-t-2 my-2 border-gray-300 block w-full"></span>
                <div class="flex justify-between space-x-4 px-6 text-sm">
                    <p class="w-1/2 break-words line-clamp-4">{{ $document->terms }}</p>
                    <p class="w-1/2 break-words line-clamp-4">{{ $document->footer }}</p>
                </div>
            @else
                @if ($document->footer)
                    <div class="px-6 text-sm text-center">
                        <p class="break-words line-clamp-4">{{ $document->footer }}</p>
                    </div>
                @endif
            @endif
        </x-company.document-template.footer>
    @endif

    @if ($document->showQRCode && $document->qrCode)
        <div class="qr-code-section p-6 text-center">
            <img src="{{ $document->qrCode->url }}" alt="QR Code" class="mx-auto"
                style="width: 100px; height: 100px;">
        </div>
    @endif

</x-company.document-template.container>
