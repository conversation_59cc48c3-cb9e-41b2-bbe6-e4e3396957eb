<?php

namespace App\Listeners;

use App\Events\CompanyConfigured;
use App\Models\Company;
use App\Utilities\ConfigureCurrencies;
use Illuminate\Support\Facades\Log;

class ConfigureCompanyDefault
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(CompanyConfigured $event): void
    {
        $company = $event->company;

        Log::info("Configuration de l'entreprise {$company->name} (ID: {$company->id})");

        try {
            // 1. Configurer les paramètres d'environnement
            $this->configureEnvironmentSettings($company);

            // 2. Synchroniser les devises
            ConfigureCurrencies::syncCurrencies();
            Log::info("Configuration terminée pour l'entreprise {$company->id}");

        } catch (\Exception $e) {
            Log::error("Erreur lors de la configuration de l'entreprise {$company->id}: " . $e->getMessage());
        }
    }

    /**
     * Configure les paramètres d'environnement de l'application
     */
    private function configureEnvironmentSettings(Company $company): void
    {
        $locale = $company->locale;

        if ($locale) {
            // Configurer la langue
            if ($locale->language) {
                app()->setLocale($locale->language);
                locale_set_default($locale->language);
            }

            // Configurer le fuseau horaire
            if ($locale->timezone) {
                config(['app.timezone' => $locale->timezone]);
                date_default_timezone_set($locale->timezone);
            }

            Log::info("Paramètres d'environnement configurés pour l'entreprise {$company->id}");
        }
    }
}
