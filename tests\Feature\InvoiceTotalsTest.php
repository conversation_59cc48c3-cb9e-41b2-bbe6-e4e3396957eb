<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Client;
use App\Models\Company;
use App\Models\Invoice;
use App\Models\Offering;
use App\Models\Adjustment;
use App\Enums\InvoiceStatus;
use App\Enums\AdjustmentType;
use App\Enums\AdjustmentCategory;
use App\Enums\AdjustmentComputation;
use App\Enums\DocumentDiscountMethod;
use Illuminate\Foundation\Testing\RefreshDatabase;

class InvoiceTotalsTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Company $company;
    protected Client $client;
    protected Offering $offering;
    protected Adjustment $tax;

    protected function setUp(): void
    {
        parent::setUp();

        // Créer une entreprise et un utilisateur
        $this->company = Company::factory()->create();
        $this->user = User::factory()->create();
        $this->user->companies()->attach($this->company->id);
        $this->user->update(['current_company_id' => $this->company->id]);

        // Créer un client
        $this->client = Client::factory()->create([
            'company_id' => $this->company->id,
        ]);

        // Créer un produit/service
        $this->offering = Offering::factory()->create([
            'company_id' => $this->company->id,
            'name' => 'Test Service',
            'price' => 10000, // 100.00 en centimes
            'sellable' => true,
        ]);

        // Créer une taxe
        $this->tax = Adjustment::factory()->create([
            'company_id' => $this->company->id,
            'name' => 'TVA',
            'category' => AdjustmentCategory::Tax,
            'type' => AdjustmentType::Sales,
            'computation' => AdjustmentComputation::Percentage,
            'rate' => 2000, // 20% en centimes
        ]);
    }

    public function test_invoice_totals_are_calculated_correctly_on_creation()
    {
        $this->actingAs($this->user);

        $invoiceData = [
            'company_id' => $this->company->id,
            'client_id' => $this->client->id,
            'invoice_number' => 'INV-001',
            'date' => now(),
            'status' => InvoiceStatus::Draft,
            'currency_code' => 'EUR',
            'discount_method' => DocumentDiscountMethod::PerLineItem,
            'lineItems' => [
                [
                    'offering_id' => $this->offering->id,
                    'description' => 'Test Service',
                    'quantity' => 2,
                    'unit_price' => 100.00,
                    'salesTaxes' => [$this->tax->id],
                    'salesDiscounts' => [],
                ]
            ]
        ];

        $invoice = Invoice::create($invoiceData);

        // Simuler la logique de création comme dans CreateInvoice
        $trait = new class {
            use \App\Traits\ManagesLineItems;
            
            public function testHandleLineItems($record, $lineItems) {
                $this->handleLineItems($record, collect($lineItems));
            }
            
            public function testUpdateDocumentTotals($record, $data) {
                return $this->updateDocumentTotals($record, $data);
            }
        };

        $trait->testHandleLineItems($invoice, $invoiceData['lineItems']);
        $totals = $trait->testUpdateDocumentTotals($invoice, $invoiceData);
        $invoice->update($totals);

        // Vérifier que les totaux sont corrects
        $invoice->refresh();
        
        // Subtotal: 2 * 100.00 = 200.00
        $this->assertEquals(20000, $invoice->getRawOriginal('subtotal'));
        
        // Tax: 20% de 200.00 = 40.00
        $this->assertEquals(4000, $invoice->getRawOriginal('tax_total'));
        
        // Total: 200.00 + 40.00 = 240.00
        $this->assertEquals(24000, $invoice->getRawOriginal('total'));

        // Vérifier que les lineItems ont été créés
        $this->assertCount(1, $invoice->lineItems);
        
        $lineItem = $invoice->lineItems->first();
        $this->assertEquals(2, $lineItem->quantity);
        $this->assertEquals(10000, $lineItem->getRawOriginal('unit_price')); // 100.00 en centimes
        $this->assertEquals(20000, $lineItem->getRawOriginal('subtotal')); // 200.00 en centimes
        $this->assertEquals(4000, $lineItem->getRawOriginal('tax_total')); // 40.00 en centimes
    }
}
