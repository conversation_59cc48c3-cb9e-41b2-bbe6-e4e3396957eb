<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Department>
 */
class DepartmentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->randomElement([
                'Human Resources',
                'Information Technology',
                'Finance',
                'Marketing',
                'Sales',
                'Operations',
                'Customer Service',
                'Research & Development',
                'Legal',
                'Administration',
                'Quality Assurance',
                'Procurement',
                'Logistics',
                'Training',
                'Security',
            ]),
            'description' => $this->faker->sentence(10),
        ];
    }
}
