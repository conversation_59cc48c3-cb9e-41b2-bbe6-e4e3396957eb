<?php

namespace App\DTO;

use App\Models\Client;

readonly class ClientDTO
{
    public function __construct(
        public string $name,
        public string $fiscal_number,
        public string $address,
        public string $contact,
    ) {
    }

    public static function fromModel(Client $client): self
    {

        return new self(
            name: $client->name ?? '',
            fiscal_number: $client->fiscal_number ?? '',
            address: $client->address ?? '',
            contact: $client->contact ?? '',

        );
    }

    public function getFormattedAddressHtml(): ?string
    {
        if (empty($this->address)) {
            return null;
        }

        return "<p>{$this->address}</p>";
    }

}
