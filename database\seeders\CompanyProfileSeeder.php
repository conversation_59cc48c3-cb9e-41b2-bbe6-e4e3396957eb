<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\CompanyProfile;
use App\Models\Address;
use App\Enums\EntityType;
use App\Enums\AddressType;
use Illuminate\Database\Seeder;

class CompanyProfileSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $companies = Company::all();

        if ($companies->isEmpty()) {
            $this->command->warn('Aucune entreprise trouvée. Exécutez CompanySeeder en premier.');
            return;
        }

        foreach ($companies->take(3) as $index => $company) {
            $profiles = [
                [
                    'company_id' => $company->id,
                    'entity_type' => EntityType::LimitedLiabilityCompany,
                    'email' => '<EMAIL>',
                    'rccm' => 'CI-ABJ-2023-B-12345',
                    'fiscal_number' => '1234567890123',
                    'siret_number' => '1234567890',
                    'currency_code' => 'EUR',
                    'tax_id' => 'FR12345678901',
                    'created_by' => $company->user_id,
                    'updated_by' => $company->user_id,
                ],
                [
                    'company_id' => $company->id,
                    'entity_type' => EntityType::Corporation,
                    'email' => '<EMAIL>',
                    'rccm' => 'CI-ABJ-2023-B-54321',
                    'fiscal_number' => '9876543210987',
                    'siret_number' => '9876543210',
                    'currency_code' => 'XOF',
                    'tax_id' => 'CI98765432109',
                    'created_by' => $company->user_id,
                    'updated_by' => $company->user_id,
                ],
                [
                    'company_id' => $company->id,
                    'entity_type' => EntityType::SoleProprietorship,
                    'email' => '<EMAIL>',
                    'rccm' => 'CI-ABJ-2023-B-11111',
                    'fiscal_number' => '1111111111111',
                    'siret_number' => '1111111111',
                    'currency_code' => 'USD',
                    'tax_id' => 'US111111111',
                    'created_by' => $company->user_id,
                    'updated_by' => $company->user_id,
                ],
            ];

            // Vérifier si le profil existe déjà
            $existingProfile = CompanyProfile::where('company_id', $company->id)->first();
            if (!$existingProfile) {
                $profile = CompanyProfile::create($profiles[$index] ?? $profiles[0]);

                // Créer l'adresse polymorphique pour le profil d'entreprise
                $this->createAddressForProfile($profile);
            }
        }

        // Profils supplémentaires avec factory (seulement si pas déjà existants)
        $remainingCompanies = $companies->skip(3);
        foreach ($remainingCompanies as $company) {
            $existingProfile = CompanyProfile::where('company_id', $company->id)->first();
            if (!$existingProfile) {
                CompanyProfile::factory()->create([
                    'company_id' => $company->id,
                    'created_by' => $company->user_id,
                    'updated_by' => $company->user_id,
                ]);
            }
        }

        $this->command->info('Profils d\'entreprise créés avec succès !');
    }

    /**
     * Créer une adresse polymorphique pour un profil d'entreprise
     */
    private function createAddressForProfile(CompanyProfile $profile): void
    {
        $addresses = [
            [
                'type' => AddressType::General,
                'recipient' => $profile->company->name,
                'phone' => '+33 1 23 45 67 89',
                'address_line_1' => '123 Rue de la Technologie',
                'address_line_2' => 'Bâtiment A, 2ème étage',
                'city' => 'Paris',
                'postal_code' => '75001',
                'country_code' => 'FR',
                'notes' => 'Siège social',
            ],
            [
                'type' => AddressType::Billing,
                'recipient' => $profile->company->name,
                'phone' => '+225 20 12 34 56',
                'address_line_1' => '456 Boulevard de la République',
                'address_line_2' => 'Zone 4',
                'city' => 'Abidjan',
                'postal_code' => '01 BP 1234',
                'country_code' => 'CI',
                'notes' => 'Bureau principal',
            ],
            [
                'type' => AddressType::General,
                'recipient' => $profile->company->name,
                'phone' => '****** 123 4567',
                'address_line_1' => '789 Innovation Drive',
                'address_line_2' => 'Suite 100',
                'city' => 'New York',
                'postal_code' => '10001',
                'country_code' => 'US',
                'notes' => 'Headquarters',
            ],
        ];

        // Utiliser l'index basé sur l'ID pour avoir des adresses différentes
        $addressIndex = ($profile->id - 1) % count($addresses);
        $addressData = $addresses[$addressIndex];

        $profile->address()->create(array_merge($addressData, [
            'company_id' => $profile->company_id,
            'created_by' => $profile->created_by,
            'updated_by' => $profile->updated_by,
        ]));
    }
}
