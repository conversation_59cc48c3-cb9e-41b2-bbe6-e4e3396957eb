<?php

namespace App\Models;

use App\Traits\Blamable;
use App\Traits\CompanyOwned;
use Illuminate\Database\Eloquent\Model;
use Database\Factories\DepartmentFactory;
use Wallo\FilamentCompanies\FilamentCompanies;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Department extends Model
{
    use HasFactory;
    use Blamable;
    use CompanyOwned;

    protected $table = 'departments';

    protected $fillable = [
        'company_id',
        'manager_id',
        'parent_id',
        'name',
        'description',
        'created_by',
        'updated_by',
    ];

    public function manager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id')
            ->whereKeyNot($this->getKey());
    }

    public function children(): Has<PERSON>any
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    public function employeeships(): HasMany
    {
        return $this->hasMany(FilamentCompanies::employeeshipModel(), 'department_id');
    }

    protected static function newFactory(): Factory
    {
        return DepartmentFactory::new();
    }
}
