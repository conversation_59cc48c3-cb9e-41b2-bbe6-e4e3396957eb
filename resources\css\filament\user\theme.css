@import "../../../../vendor/filament/filament/resources/css/theme.css";
@import "form-fields.css";
@import "../../../../vendor/awcodes/filament-table-repeater/resources/css/plugin.css";

@config 'tailwind.config.js';

.fi-body {
    position: relative;
    background-color: #e8e9eb;
    z-index: 1;
}

.fi-body::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(
        99.6deg,
        rgba(232, 233, 235, 1) 10.6%,
        rgba(240, 241, 243, 1) 32.9%,
        rgba(248, 249, 251, 0.7) 50%,
        rgba(240, 241, 243, 1) 67.1%,
        rgba(232, 233, 235, 1) 83.4%
    );
    pointer-events: none;
    z-index: -1;
}

:is(.dark .fi-body) {
    position: relative;
    background-color: rgb(3, 7, 18);
    z-index: 1;
}

:is(.dark .fi-body)::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    background-image: radial-gradient(
        ellipse at top right,
        rgba(var(--primary-950), 1) 0%,
        rgba(var(--primary-950), 0.9) 15%,
        rgba(var(--primary-900), 0.7) 30%,
        rgba(var(--primary-900), 0.5) 45%,
        rgba(var(--primary-950), 0.3) 60%,
        rgba(var(--primary-950), 0.1) 75%,
        rgba(3, 7, 18, 0) 100%
    );
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.fi-topbar > nav,
.fi-sidebar-header {
    @apply bg-transparent ring-0 shadow-none !important;
    transition: background-color 0.3s, top 0.3s;
}

.fi-topbar > nav {
    @apply relative flex justify-center;

    > ul {
        @apply absolute -translate-x-2/4 gap-x-0 left-2/4;
    }

    > div {
        @apply static;
    }
}

.fi-topbar > nav.topbar-hovered,
.fi-sidebar-header.topbar-hovered {
    background-color: rgba(255, 255, 255, 0.75) !important;
}

:is(
        .dark .fi-topbar > nav.topbar-hovered,
        .dark .fi-sidebar-header.topbar-hovered
    ) {
    background-color: rgba(10, 16, 33, 0.75) !important;
}

.fi-topbar > nav.topbar-scrolled,
.fi-sidebar-header.topbar-scrolled {
    background-color: rgba(255, 255, 255, 0.5) !important;
}

:is(
        .dark .fi-topbar > nav.topbar-scrolled,
        .dark .fi-sidebar-header.topbar-scrolled
    ) {
    background-color: rgba(10, 16, 33, 0.5) !important;
}

.fi-dropdown.fi-topbar-dropdown .fi-dropdown-panel {
    transition: max-width 0.3s linear 0.1s, opacity 0.1s linear 0.1s;
    @apply absolute -translate-x-1/2 left-1/2 !important;
}

.fi-topbar-item > * {
    @apply h-16 rounded-none !bg-transparent border-b-2 border-transparent;
}

.fi-topbar-item > button > svg {
    transition: transform 0.1s ease-in-out, color 0.1s ease-in-out;
}

.fi-topbar-item-active {
    & > a,
    & > button {
        @apply border-primary-600 dark:border-primary-400;
    }

    > button {
        @apply text-gray-700 dark:text-gray-200;
    }

    > button > svg {
        @apply text-gray-400 dark:text-gray-500;
    }
}

.fi-topbar-item > a:hover,
a:focus-visible {
    @apply text-primary-600 dark:text-primary-400 border-primary-600 dark:border-primary-400;

    > svg {
        @apply text-primary-500;
    }
}

.fi-topbar-dropdown[aria-expanded="true"],
.fi-topbar-dropdown[aria-expanded="false"]
    .fi-dropdown-trigger[aria-expanded="true"] {
    .fi-topbar-item > button {
        @apply text-primary-600 dark:text-primary-400 border-primary-600 dark:border-primary-400 !important;

        > svg {
            @apply rotate-180 text-primary-500;
        }
    }
}

.fi-sidebar-nav {
    scrollbar-width: thin;
}

.menu-groups {
    @apply flex flex-row relative transition-[unset] m-0 px-12 py-9;

    .menu-group {
        @apply flex flex-col justify-between relative z-[3] mx-2.5 my-0 p-0;
    }
}

.submenu-wrap {
    .submenu {
        @apply flex flex-wrap m-0 p-0;
    }

    .submenu.cols-1 {
        @apply w-60;
    }

    .menu-label,
    .menu-label-invisible-spacer {
        @apply text-gray-700 dark:text-gray-200 text-base font-medium mb-4 pb-4 border-b border-gray-200 dark:border-gray-700 min-h-[2.5625rem];
    }

    .menu-label-invisible-spacer {
        @apply invisible;
    }
}

.menu-item {
    @apply cursor-pointer -left-4 leading-tight relative w-[calc(100%_-_10px)] p-2;
}

.fi-topbar-dropdown-list-item {
    @apply flex justify-between relative transition-[left] duration-200 z-[2] rounded-none left-0;

    &::after {
        @apply bg-primary-600 dark:bg-primary-400 content-[''] block h-0 left-[-1rem] absolute transition-all duration-300 w-px top-[calc(100%_+_0.5rem)];
    }

    &:hover {
        @apply left-4 !bg-transparent;
    }

    &:hover > span {
        @apply text-primary-600 dark:text-primary-400;
    }

    &:hover::after {
        @apply -top-2 h-[calc(100%+1rem)];
    }

    &:hover .bg {
        @apply w-full;
    }

    > span {
        @apply flex flex-col relative z-[1] font-medium;
    }

    .bg {
        @apply h-[calc(100%+1rem)] -left-4 absolute -top-2 transition-[width] duration-200 delay-100 bg-gradient-to-r from-primary-600/10 dark:from-primary-400/10;
    }
}

.choices__inner {
    height: 2.25rem;
}

.fi-badge {
    display: inline-flex;
}

/* Next Cpnfg */

.es-file-upload.document-logo > div {
    @apply w-48;
}

.es-file-upload.document-logo-preview > div {
    @apply w-40;
}
/*
.fi-in-text-item .group-hover\/item\:underline, .fi-ta-text-item .group-hover\/item\:underline {
    @apply text-primary-600 dark:text-primary-400 font-semibold;
}

.fi-sidebar-nav {
    scrollbar-width: thin;
}

.fi-ta-empty-state-icon-ctn {
    @apply bg-platinum;
}

.fi-dropdown-panel {
    @apply divide-gray-200/80;
}

.fi-badge {
    display: inline-flex;
} */

/* End Alignment sortable column enhancement */
.fi-ta-header-cell:has(button.justify-end > svg.fi-ta-header-cell-sort-icon) {
    padding-right: 0;
}

/* Transparent backgrounds for all Filament form layouts in dark theme */
:is(.dark .fi-fo-section),
:is(.dark .fi-section),
:is(.dark .fi-fo-card),
:is(.dark .fi-card),
:is(.dark .fi-fo-group),
:is(.dark .fi-group),
:is(.dark .fi-fo-fieldset),
:is(.dark .fi-fieldset),
:is(.dark .fi-fo-tabs),
:is(.dark .fi-tabs),
:is(.dark .fi-fo-tabs-tab),
:is(.dark .fi-tabs-tab),
:is(.dark .fi-fo-wizard),
:is(.dark .fi-wizard),
:is(.dark .fi-fo-wizard-step),
:is(.dark .fi-wizard-step),
:is(.dark .fi-modal),
:is(.dark .fi-modal-content),
:is(.dark .fi-slideover),
:is(.dark .fi-slideover-content),
:is(.dark .fi-fo-layout),
:is(.dark .fi-layout),
:is(.dark .fi-fo-container),
:is(.dark .fi-container),
:is(.dark .fi-fo-panel),
:is(.dark .fi-panel),
:is(.dark .fi-form),
:is(.dark .fi-form-component-ctn),
:is(.dark .fi-form-section),
:is(.dark .fi-form-card),
:is(.dark .fi-form-group),
:is(.dark .fi-form-fieldset) {
    @apply bg-transparent !important;
}

/* Transparent backgrounds for all Filament table layouts and columns in dark theme */
:is(.dark .fi-ta-table),
:is(.dark .fi-table),
:is(.dark .fi-ta-ctn),
:is(.dark .fi-ta-container),
:is(.dark .fi-ta-content),
:is(.dark .fi-ta-header),
:is(.dark .fi-ta-header-cell),
:is(.dark .fi-ta-header-cell-label),
:is(.dark .fi-ta-header-ctn),
:is(.dark .fi-ta-row),
:is(.dark .fi-ta-cell),
:is(.dark .fi-ta-record),
:is(.dark .fi-ta-col),
:is(.dark .fi-ta-col-wrp),
:is(.dark .fi-ta-text),
:is(.dark .fi-ta-text-item),
:is(.dark .fi-ta-icon),
:is(.dark .fi-ta-icon-item),
:is(.dark .fi-ta-badge),
:is(.dark .fi-ta-badge-item),
:is(.dark .fi-ta-image),
:is(.dark .fi-ta-image-item),
:is(.dark .fi-ta-color),
:is(.dark .fi-ta-color-item),
:is(.dark .fi-ta-toggle),
:is(.dark .fi-ta-toggle-item),
:is(.dark .fi-ta-select),
:is(.dark .fi-ta-select-item),
:is(.dark .fi-ta-text-input),
:is(.dark .fi-ta-text-input-item),
:is(.dark .fi-ta-summary),
:is(.dark .fi-ta-summary-row),
:is(.dark .fi-ta-summary-cell),
:is(.dark .fi-ta-summary-row-heading),
:is(.dark .fi-ta-text-summary),
:is(.dark .fi-ta-actions),
:is(.dark .fi-ta-actions-item),
:is(.dark .fi-ta-bulk-actions),
:is(.dark .fi-ta-filters),
:is(.dark .fi-ta-filters-form),
:is(.dark .fi-ta-search),
:is(.dark .fi-ta-search-field),
:is(.dark .fi-ta-pagination),
:is(.dark .fi-ta-empty-state),
:is(.dark .fi-ta-loading-indicator) {
    @apply bg-transparent !important;
}

/* Transparent backgrounds for all remaining Filament components in dark theme */
:is(.dark .fi-ac-action),
:is(.dark .fi-ac-btn-group),
:is(.dark .fi-ac-icon-btn-group),
:is(.dark .fi-ac-link-group),
:is(.dark .fi-ac-badge-group),
:is(.dark .fi-ac-grouped-action),
:is(.dark .fi-ac-grouped-group),
:is(.dark .fi-dropdown),
:is(.dark .fi-dropdown-panel),
:is(.dark .fi-dropdown-trigger),
:is(.dark .fi-dropdown-list),
:is(.dark .fi-dropdown-list-item),
:is(.dark .fi-dropdown-header),
:is(.dark .fi-modal-window),
:is(.dark .fi-modal-slide-over-window),
:is(.dark .fi-modal-trigger),
:is(.dark .fi-slideover-window),
:is(.dark .fi-in-section),
:is(.dark .fi-in-card),
:is(.dark .fi-in-group),
:is(.dark .fi-in-fieldset),
:is(.dark .fi-in-tabs),
:is(.dark .fi-in-tabs-tab),
:is(.dark .fi-in-layout),
:is(.dark .fi-in-container),
:is(.dark .fi-in-panel),
:is(.dark .fi-in-text),
:is(.dark .fi-in-text-item),
:is(.dark .fi-in-icon),
:is(.dark .fi-in-icon-item),
:is(.dark .fi-in-image),
:is(.dark .fi-in-image-item),
:is(.dark .fi-in-color),
:is(.dark .fi-in-color-item),
:is(.dark .fi-in-key-value),
:is(.dark .fi-in-key-value-item),
:is(.dark .fi-in-repeatable),
:is(.dark .fi-in-repeatable-item),
:is(.dark .fi-wi),
:is(.dark .fi-wi-widget),
:is(.dark .fi-wi-stats-overview),
:is(.dark .fi-wi-chart),
:is(.dark .fi-wi-table),
:is(.dark .fi-wi-stats-overview-card),
:is(.dark .fi-wi-stats-overview-stat),
:is(.dark .fi-no),
:is(.dark .fi-no-notification),
:is(.dark .fi-no-database-notifications),
:is(.dark .fi-no-broadcast-notifications),
:is(.dark .fi-avatar),
:is(.dark .fi-badge),
:is(.dark .fi-breadcrumbs),
:is(.dark .fi-button),
:is(.dark .fi-icon-button),
:is(.dark .fi-link),
:is(.dark .fi-loading-indicator),
:is(.dark .fi-loading-section),
:is(.dark .fi-pagination),
:is(.dark .fi-grid),
:is(.dark .fi-simple-layout),
:is(.dark .fi-simple-main-ctn),
:is(.dark .fi-simple-main) {
    @apply bg-transparent !important;
}

/* Hover effects for all Filament components in dark theme (excluding table cells, form layouts and dropdowns) */
/* :is(.dark .fi-fo-section):hover, */
/* :is(.dark .fi-section):hover, */
/* :is(.dark .fi-fo-card):hover, */
/* :is(.dark .fi-card):hover, */
/* :is(.dark .fi-fo-group):hover, */
/* :is(.dark .fi-group):hover, */
/* :is(.dark .fi-ta-row):hover, */
/* :is(.dark .fi-ta-cell):hover, */
/* :is(.dark .fi-dropdown-panel):hover, */
/* :is(.dark .fi-dropdown-list-item):hover, */
:is(.dark .fi-modal-window):hover,
:is(.dark .fi-ac-action):hover,
:is(.dark .fi-ac-btn-group):hover,
:is(.dark .fi-ac-icon-btn-group):hover,
:is(.dark .fi-button):hover,
:is(.dark .fi-icon-button):hover,
:is(.dark .fi-link):hover,
:is(.dark .fi-wi-widget):hover,
:is(.dark .fi-wi-stats-overview-card):hover,
:is(.dark .fi-badge):hover {
    @apply bg-white/5 transition-colors duration-200 !important;
}

/* Focus effects for interactive Filament components in dark theme */
:is(.dark .fi-button):focus,
:is(.dark .fi-icon-button):focus,
:is(.dark .fi-link):focus,
:is(.dark .fi-dropdown-trigger):focus,
:is(.dark .fi-ac-action):focus,
:is(.dark .fi-ta-text-input):focus {
    @apply bg-white/10 ring-2 ring-primary-500/50 transition-all duration-200 !important;
}

/* RadioDeck transparent backgrounds in dark theme */
:is(.dark) label div.bg-white.dark\:bg-gray-900 {
    @apply bg-transparent !important;
}

/* Disable hover effects for RadioDeck cards */
:is(.dark) label div.bg-white.dark\:bg-gray-900:hover {
    @apply bg-transparent !important;
}

/* Transparent backgrounds for Select dropdown options in dark theme */
:is(.dark) .choices__list--dropdown,
:is(.dark) .choices__list--dropdown .choices__list,
:is(.dark) .choices__item--choice,
:is(.dark) .choices__group,
:is(.dark) .choices__placeholder {
    @apply bg-transparent !important;
}

/* Hover effects for Select dropdown options in dark theme */
:is(.dark) .choices__item--choice:hover,
:is(.dark) .choices__item--choice.is-highlighted {
    @apply bg-white/5 transition-colors duration-200 !important;
}

/* :not(.dark) .fi-body {
    position: relative;
    background-color: #E8E9EB;
    z-index: 1;
}

:not(.dark) .fi-body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(99.6deg,
    rgba(232, 233, 235, 1) 10.6%,
    rgba(240, 241, 243, 1) 32.9%,
    rgba(248, 249, 251, 0.7) 50%,
    rgba(240, 241, 243, 1) 67.1%,
    rgba(232, 233, 235, 1) 83.4%);
    pointer-events: none;
    z-index: -1;
} */

.fi-ta-table:has(.is-spreadsheet) {
    .fi-ta-row {
        @apply divide-x divide-gray-200 dark:divide-gray-700;
    }

    .fi-ta-summary-row-heading {
        @apply px-3 py-3 !important;
    }

    .fi-ta-text-summary {
        @apply px-3 py-3 !important;

        > span {
            @apply font-medium text-gray-950 dark:text-white;
        }
    }

    .fi-ta-text {
        @apply px-3 py-0 !important;
    }

    .fi-ta-text-input {
        @apply px-0 py-0 !important;
    }

    .fi-ta-icon {
        @apply px-3 py-0 !important;
    }

    .fi-ta-text-input {
        .fi-input-wrp,
        .fi-input {
            @apply ring-0 bg-transparent shadow-none rounded-none !important;
        }
    }

    .fi-ta-cell:has(.fi-ta-text-input):focus-within {
        outline: 2px solid #2563eb;
        outline-offset: -2px;
        z-index: 1;
    }

    .fi-ta-col-wrp button:focus {
        outline: none !important;
        box-shadow: none !important;
    }
}

.doc-template-container {
    color: initial;
}

.doc-template-container .overflow-x-auto,
.doc-template-paper {
    scrollbar-width: thin;
}

.doc-template-paper.preview {
    zoom: 0.5;
}

@media (min-width: 1024px) {
    .doc-template-paper.preview {
        zoom: 0.85;
    }
}

.doc-template-paper:not(.preview) {
    zoom: 0.65;
}

@media (min-width: 1024px) {
    .doc-template-paper:not(.preview) {
        zoom: 1;
    }
}
