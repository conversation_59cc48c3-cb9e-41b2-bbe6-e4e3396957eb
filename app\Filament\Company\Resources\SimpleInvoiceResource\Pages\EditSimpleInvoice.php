<?php

namespace App\Filament\Company\Resources\SimpleInvoiceResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Company\Resources\SimpleInvoiceResource;

class EditSimpleInvoice extends EditRecord
{
    protected static string $resource = SimpleInvoiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Add tax_rate for display (calculate from tax_total and subtotal)
        if ($data['subtotal'] > 0 && $data['tax_total'] > 0) {
            $data['tax_rate'] = ($data['tax_total'] / $data['subtotal']) * 100;
        } else {
            $data['tax_rate'] = 0;
        }
        
        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Calculate totals based on line items and tax rate
        $lineItems = $data['lineItems'] ?? [];
        $taxRate = (float) ($data['tax_rate'] ?? 0);
        
        $subtotal = collect($lineItems)->sum(function ($item) {
            return ((float) ($item['quantity'] ?? 0)) * ((float) ($item['unit_price'] ?? 0));
        });
        
        $taxTotal = $subtotal * ($taxRate / 100);
        $total = $subtotal + $taxTotal;
        
        // Set calculated values
        $data['subtotal'] = $subtotal;
        $data['tax_total'] = $taxTotal;
        $data['discount_total'] = 0; // No discount in simple version
        $data['total'] = $total;
        
        // Update amount_due (total - amount_paid)
        $amountPaid = $data['amount_paid'] ?? 0;
        $data['amount_due'] = $total - $amountPaid;
        
        // Remove tax_rate as it's not a field in the model
        unset($data['tax_rate']);
        
        return $data;
    }

    protected function afterSave(): void
    {
        // Update line items with calculated totals
        $record = $this->record;
        
        foreach ($record->lineItems as $index => $lineItem) {
            $quantity = $lineItem->quantity;
            $unitPrice = $lineItem->unit_price;
            $subtotal = $quantity * $unitPrice;
            
            $lineItem->update([
                'subtotal' => $subtotal,
                'total' => $subtotal, // No taxes/discounts per line in simple version
                'tax_total' => 0,
                'discount_total' => 0,
            ]);
        }
    }
}
