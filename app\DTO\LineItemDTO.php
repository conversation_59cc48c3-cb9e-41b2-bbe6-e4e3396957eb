<?php

namespace App\DTO;

use App\Models\DocumentLineItem;
use App\Utilities\CurrencyConverter;

readonly class LineItemDTO
{
    public function __construct(
        public string $name,
        public ?string $description = null,
        public ?int $quantity = null,
        public ?string $unitPrice = null,
        public ?string $subtotal = null,
        // Champs pour les livraisons
        public ?string $delivery = null,
        public ?string $receiver = null,
        public ?string $remarks = null,
    ) {}

    public static function fromModel(DocumentLineItem $lineItem): self
    {
        $currencyCode = $lineItem->documentable->currency_code;

        return new self(
            name: $lineItem->offering->name ?? '',
            description: $lineItem->description,
            quantity: $lineItem->quantity,
            unitPrice: self::formatToMoney($lineItem->unit_price, $currencyCode),
            subtotal: self::formatToMoney($lineItem->subtotal, $currencyCode),
            delivery: $lineItem->delivery_person,
            receiver: $lineItem->receiver_person,
            remarks: $lineItem->delivery_remarks,
        );
    }

    public static function fake(bool $isDelivery = false): self
    {
        if ($isDelivery) {
            return new self(
                name: 'Produit de livraison',
                description: 'Description du produit à livrer',
                delivery: 'Adresse de livraison',
                receiver: 'Nom du destinataire',
                remarks: 'Remarques spéciales',
            );
        }

        return new self(
            name: 'Professional Services',
            description: 'Consulting and strategic planning',
            quantity: 2,
            unitPrice: self::formatToMoney(15000, null),
            subtotal: self::formatToMoney(30000, null),
        );
    }

    public static function fakeItems(bool $isDelivery = false): array
    {
        if ($isDelivery) {
            return [
                self::fake(true),
                new self(
                    name: 'Autre produit',
                    description: 'Autre description',
                    delivery: 'Autre adresse',
                    receiver: 'Autre destinataire',
                    remarks: 'Autres remarques',
                ),
            ];
        }

        return [
            self::fake(),
            new self(
                name: 'Software License',
                description: 'Annual subscription and support',
                quantity: 3,
                unitPrice: self::formatToMoney(20000, null),
                subtotal: self::formatToMoney(60000, null),
            ),
            new self(
                name: 'Training Session',
                description: 'Team onboarding and documentation',
                quantity: 1,
                unitPrice: self::formatToMoney(10000, null),
                subtotal: self::formatToMoney(10000, null),
            ),
        ];
    }

    protected static function formatToMoney(float|string|int $value, ?string $currencyCode): string
    {
        return CurrencyConverter::formatToMoney($value, $currencyCode);
    }
}
