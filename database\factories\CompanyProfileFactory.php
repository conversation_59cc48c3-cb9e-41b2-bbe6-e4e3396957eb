<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CompanyProfile>
 */
class CompanyProfileFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'entity_type' => \App\Enums\EntityType::Corporation,
            'email' => $this->faker->companyEmail(),
            'rccm' => 'CI-ABJ-' . $this->faker->year() . '-B-' . $this->faker->numerify('#####'),
            'fiscal_number' => $this->faker->numerify('##########'),
            'siret_number' => $this->faker->numerify('##########'),
            'currency_code' => 'EUR',
            'tax_id' => $this->faker->numerify('##########'),
        ];
    }
}
