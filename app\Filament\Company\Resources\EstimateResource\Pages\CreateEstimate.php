<?php

namespace App\Filament\Company\Resources\EstimateResource\Pages;

use App\Filament\Company\Resources\EstimateResource;
use App\Traits\ManagesLineItems;
use Filament\Resources\Pages\CreateRecord;

class CreateEstimate extends CreateRecord
{
    use ManagesLineItems;

    protected static string $resource = EstimateResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['company_id'] = auth()->user()->currentCompany->id;
        $data['created_by'] = auth()->id();
        $data['updated_by'] = auth()->id();

        return $data;
    }

    protected function afterCreate(): void
    {
        // Gérer les lineItems après la création du devis
        $data = $this->form->getState();
        if (isset($data['lineItems'])) {
            $this->handleLineItems($this->record, collect($data['lineItems']));
            
            // Calculer et sauvegarder les totaux
            $totals = $this->updateDocumentTotals($this->record, $data);
            $this->record->updateQuietly($totals);
        }
    }
}
