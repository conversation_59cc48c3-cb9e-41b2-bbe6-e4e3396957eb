<?php

namespace App\Traits;

use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

trait NotifiesOnDelete
{
    public static function notifyBeforeDelete(Model $record, string $reason): void
    {
        $trueReason = $reason;

        Notification::make()
            ->danger()
            ->title(('Action denied'))
            ->body(':Name cannot be deleted because it is :reason. Please update settings before deletion.', [
                'Name' => $record->getAttribute('name'),
                'reason' => $trueReason,
            ])
            ->persistent()
            ->send();
    }

    public static function notifyBeforeDeleteMultiple(Collection $records, string $reason): void
    {
        $trueReason = $reason;

        $namesList = implode('<br>', array_map(static function ($record) {
            return '&bull; ' . $record->getAttribute('name');
        }, $records->all()));

        $message = 'The following items cannot be deleted because they are :reason. Please update settings before deletion.'. compact('trueReason') . '<br><br>';

        $message .= $namesList;

        Notification::make()
            ->danger()
            ->title('Action denied')
            ->body($message)
            ->persistent()
            ->send();
    }
}
