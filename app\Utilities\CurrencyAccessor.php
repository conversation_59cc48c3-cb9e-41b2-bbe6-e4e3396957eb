<?php

namespace App\Utilities;

use App\Services\CompanySettingsService;
use Akaunting\Money\Currency as ISOCurrency;

class CurrencyAccessor
{
    /**
     * Retourne toutes les devises sous forme d’options lisibles pour un champ Select.
     *
     * @return array<string, string>
     */
    public static function getAllCurrencyOptions(): array
    {
        return collect(ISOCurrency::getCurrencies())
            ->mapWithKeys(function (array $data, string $code) {
                $name = $data['name'] ?? $code;
                $symbol = $data['symbol'] ?? '';
                return [$code => "{$code} – {$symbol} {$name}"];
            })
            ->toArray();
    }

    /**
     * Retourne la devise par défaut de l’entreprise connectée.
     *
     * @return string|null
     */
    public static function getDefaultCurrency(): ?string
    {
        $companyId = auth()->user()?->current_company_id;

        if ($companyId === null) {
            return 'XOF'; // Valeur par défaut si aucun utilisateur connecté
        }

        return CompanySettingsService::getDefaultCurrency($companyId);
    }
}
