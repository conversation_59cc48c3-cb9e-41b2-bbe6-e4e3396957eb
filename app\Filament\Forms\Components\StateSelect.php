<?php

namespace App\Filament\Forms\Components;

use Filament\Forms\Components\Select;
use App\Models\State;
use Filament\Forms\Get;

class StateSelect extends Select
{
    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->label('State / Province')
            ->searchable()
            ->options(static fn(Get $get) => State::getStateOptions($get('country_code')))
            ->getSearchResultsUsing(static function (string $search, Get $get): array {
                return State::getSearchResultsUsing($search, $get('country_code'));
            });
    }
}
