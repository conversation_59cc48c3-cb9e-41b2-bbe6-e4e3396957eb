je veux lorsque je te demmande des requetes tu analise mon fichier composer pour savoir les packages que j'ai installer
pour pour pouvoir me donner des reponses tres pertinents et correctes en utiisant ces differentes packages installer avec leur doc  .
je veux que tu fais le moins de fautespossibles et bien suivres mes instructions


je veux une page permmettant à utilisateur de configurer ou predefini les numerotations des document juste des section
et tabs puis le package guava/cluster pour combiner les chmps et offir un exp utilisateur


<?php

namespace App\DTO;

use Carbon\Carbon;

readonly class QRCodeDTO
{
    public function __construct(
        public ?string $qrCodeImage,
        public string $invoiceCode,
        public string $documentNumber,
        public string $creationTime,
        public string $qrData,
    ) {
    }

    public static function fromDocument($document, ?string $qrCodeData = null): self
    {
        // Générer le code facture au format XXXX-XXXX-XXXX-XXXX-XXXX
        $invoiceCode = self::generateInvoiceCode($document->documentNumber());

        // Récupérer le numéro de base du document
        $documentNumber = $document->documentNumber() ?? 'N/A';

        // Formater l'heure de création
        $creationTime = $document->created_at
            ? $document->created_at->format('d/m/Y H:i:s')
            : Carbon::now()->format('d/m/Y H:i:s');

        // Créer les données QR
        $qrData = self::generateQRData($invoiceCode, $documentNumber, $creationTime);

        return new self(
            qrCodeImage: $qrCodeData,
            invoiceCode: $invoiceCode,
            documentNumber: $documentNumber,
            creationTime: $creationTime,
            qrData: $qrData,
        );
    }

    public static function fake(): self
    {
        $invoiceCode = 'INV-2024-0001-ABCD-EFGH';
        $documentNumber = 'INV-001';
        $creationTime = '23/07/2025 14:30:25';
        $qrData = self::generateQRData($invoiceCode, $documentNumber, $creationTime);

        return new self(
            qrCodeImage: null,
            invoiceCode: $invoiceCode,
            documentNumber: $documentNumber,
            creationTime: $creationTime,
            qrData: $qrData,
        );
    }

    private static function generateInvoiceCode(string $baseNumber): string
    {
        // Nettoyer le numéro de base
        $cleanNumber = preg_replace('/[^A-Za-z0-9]/', '', $baseNumber);

        // Générer des segments aléatoires pour compléter
        $segments = [
            $cleanNumber,
            strtoupper(substr(md5($cleanNumber . time()), 0, 4)),
            strtoupper(substr(md5($cleanNumber . 'salt1'), 0, 4)),
            strtoupper(substr(md5($cleanNumber . 'salt2'), 0, 4)),
            strtoupper(substr(md5($cleanNumber . 'salt3'), 0, 4)),
        ];

        return implode('-', $segments);
    }

    private static function generateQRData(string $invoiceCode, string $documentNumber, string $creationTime): string
    {
        // Format simple séparé par des pipes pour éviter les problèmes de conversion
        return $invoiceCode . '|' . $documentNumber . '|' . $creationTime . '|invoice|1.0';
    }

    public function hasQRCode(): bool
    {
        return !empty($this->qrCodeImage) || !empty($this->invoiceCode) || !empty($this->documentNumber);
    }

    public function toArray(): array
    {
        return [
            'qr_code_image' => $this->qrCodeImage,
            'invoice_code' => $this->invoiceCode,
            'document_number' => $this->documentNumber,
            'creation_time' => $this->creationTime,
            'qr_data' => $this->qrData,
        ];
    }
}
