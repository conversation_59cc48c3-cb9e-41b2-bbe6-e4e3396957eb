je veux lorsque je te demmande des requetes tu analise mon fichier composer pour savoir les packages que j'ai installer
pour pour pouvoir me donner des reponses tres pertinents et correctes en utiisant ces differentes packages installer avec leur doc .
je veux que tu fais le moins de fautespossibles et bien suivres mes instructions


je veux une page permmettant à utilisateur de configurer ou predefini les numerotations des document juste des section
et tabs puis le package guava/cluster pour combiner les chmps et offir un exp utilisateur


# Projet Laravel et Packages
    - L'utilisateur a un projet Laravel 11.31 avec Filament 3.3 comme panel d'administration principal, incluant de nombreux packages Filament pour l'interface, Laravel Money pour les devises, Livewire 3.6, et divers packages utilitaires comme Snappy pour PDF et QR code generation.

    # Instructions Spécifiques
    - L'utilisateur insiste sur le fait que je dois suivre ses consignes précisément et ne pas faire d'interprétations ou d'ajouts non demandés.
    - Le formatage des nombres, prix et montants dans l'application doit être basé sur le number_format de la localisation de l'entreprise (company localization).
    `
