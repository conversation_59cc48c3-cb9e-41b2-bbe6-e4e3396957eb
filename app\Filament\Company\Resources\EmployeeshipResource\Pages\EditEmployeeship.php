<?php

namespace App\Filament\Company\Resources\EmployeeshipResource\Pages;

use Filament\Actions;
use App\Traits\HandlePageRedirect;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Company\Resources\EmployeeshipResource;

class EditEmployeeship extends EditRecord
{
    use HandlePageRedirect;

    protected static string $resource = EmployeeshipResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
