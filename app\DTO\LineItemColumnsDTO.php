<?php

namespace App\DTO;

use App\Enums\DocumentType;
use App\Models\DocumentDefault;

readonly class LineItemColumnsDTO
{
    public function __construct(
        public DocumentType $documentType,
        public string $itemsLabel,
        public string $unitsLabel,
        public string $priceLabel,
        public string $amountLabel,
        public string $deliveryLabel,
        public string $receiverLabel,
        public string $remarksLabel,
    ) {}

    public static function fromDocumentDefault(DocumentDefault $settings): self
    {
        return new self(
            documentType: $settings->type,
            itemsLabel: self::getFieldLabel($settings->item_name, 'Items'),
            unitsLabel: self::getFieldLabel($settings->unit_name, 'Qty'),
            priceLabel: self::getFieldLabel($settings->price_name, 'Price'),
            amountLabel: self::getFieldLabel($settings->amount_name, 'Amount'),
            deliveryLabel: self::getFieldLabel($settings->delivery_person, 'Delivery'),
            receiverLabel: self::getFieldLabel($settings->receiver_person, 'Receiver'),
            remarksLabel: self::getField<PERSON>abel($settings->remarks, 'Remarks'),
        );
    }

    private static function getFieldLabel(mixed $fieldData, string $default): string
    {
        // Si pas de données, retourner la valeur par défaut
        if (!$fieldData) {
            return $default;
        }

        // Convertir ArrayObject en array si nécessaire
        if ($fieldData instanceof \ArrayObject) {
            $fieldData = $fieldData->getArrayCopy();
        }

        // Si ce n'est pas un array, retourner la valeur par défaut
        if (!is_array($fieldData)) {
            return $default;
        }

        // Priorité au label personnalisé
        if (!empty($fieldData['custom'])) {
            return $fieldData['custom'];
        }

        // Sinon utiliser les labels prédéfinis
        $labels = [
            'items' => 'Items',
            'products' => 'Products',
            'services' => 'Services',
            'quantity' => 'Qty',
            'hours' => 'Hours',
            'price' => 'Price',
            'amount' => 'Amount',
            'total' => 'Total',
            'delivery' => 'Delivery',
            'receiver' => 'Receiver',
            'remarks' => 'Remarks',
        ];

        return $labels[$fieldData['option'] ?? ''] ?? $default;
    }

    public function isDelivery(): bool
    {
        return $this->documentType === DocumentType::Delivery;
    }
}
