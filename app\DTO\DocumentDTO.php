<?php

namespace App\DTO;

use App\Enums\DocumentType;
use App\Enums\Font;
use App\Models\Document;
use App\Models\DocumentDefault;
use App\Utilities\CurrencyAccessor;
use App\Utilities\CurrencyConverter;
use Filament\FontProviders\BunnyFontProvider;
use Illuminate\Contracts\Support\Htmlable;


readonly class DocumentDTO
{
    /**
     * @param  LineItemDTO[]  $lineItems
     */
    public function __construct(
        public string $header,
        public ?string $subheader,
        public ?string $footer,
        public ?string $terms,
        public ?string $logo,
        public string $number,
        public ?string $referenceNumber,
        public string $date,
        public string $dueDate,
        public string $currencyCode,
        public ?string $subtotal,
        public ?string $discount,
        public ?string $tax,
        public string $total,
        public ?string $amountDue,
        public CompanyDTO $company,
        public ?ClientDTO $client,
        public iterable $lineItems,
        public DocumentLabelDTO $label,

        public LineItemColumnsDTO $lineItemColumns,
        public string $accentColor = '#000000',
        public bool $showLogo = true,
        public Font $font = Font::Inter,
        public bool $showPaymentDetails = true,
        public ?PaymentDetailsDTO $paymentDetails = null,
        public bool $showAmountInWords = true,
        public ?AmountInWordsDTO $amountInWords = null,
        public bool $showSignature = true,
        public ?SignatureDTO $signature = null,
        public bool $showQRCode = true,
        public ?QRCodeDTO $qrCode = null,
        public bool $showHeader = true,
        public bool $showClientDetails = true,
        public bool $showObjetDetails = true,
        public bool $showCompanyDetails = true,
        public bool $showLines = true,
        public bool $showTotals = true,
        public bool $showDiscount = true,
        public bool $showTax = true,
        public bool $showNotes = true,
        public bool $showFooter = true,
    ) {}

    public static function fromModel(Document $document): self
    {
        /** @var DocumentDefault $settings */
        $settings = $document->company->documentDefaults()
            ->type($document::documentType())
            ->first() ?? $document->company->defaultInvoice;

        $currencyCode = $document->currency_code ?? CurrencyAccessor::getDefaultCurrency();

        $discount = $document->discount_total > 0
            ? self::formatToMoney($document->discount_total, $currencyCode)
            : null;

        $tax = $document->tax_total > 0
            ? self::formatToMoney($document->tax_total, $currencyCode)
            : null;

        $subtotal = ($discount || $tax)
            ? self::formatToMoney($document->subtotal, $currencyCode)
            : null;

        $amountDue = $document::documentType() !== DocumentType::Estimate ?
            self::formatToMoney($document->amountDue(), $currencyCode) :
            null;

        // Récupérer les détails de paiement depuis les transactions
        $paymentDetails = null;
        if (method_exists($document, 'transactions')) {
            $latestTransaction = $document->transactions()->latest()->first();
            if ($latestTransaction) {
                $paymentDetails = PaymentDetailsDTO::fromTransaction($latestTransaction);
            }
        }

        // Créer le montant en lettres
        $amountInWords = null;
        if ($settings->text_with_amount_in_words) {
            $amountInWords = AmountInWordsDTO::fromAmount($document->total, $currencyCode);
        }

        // Créer la signature
        $signature = null;
        if ($settings->show_signature) {
            $signature = SignatureDTO::fromSettings(
                $settings->authorized_personnel_signature_role,
                $settings->authorized_personnel_signature_name,
                $settings->authorized_personnel_signature
            );
        }

        // Créer le QR Code
        $qrCode = null;
        if ($settings->show_qr_code) {
            $qrCode = QRCodeDTO::fromDocument($document, $settings->qr_code);
        }

        return new self(
            header: $document->header,
            subheader: $document->subheader,
            footer: $document->footer,
            terms: $document->terms,
            logo: $document->logo_url ?? $settings->logo_url,
            number: $document->documentNumber(),
            referenceNumber: $document->referenceNumber(),
            date: $document->documentDate(),
            dueDate: $document->dueDate(),
            currencyCode: $currencyCode,
            subtotal: $subtotal,
            discount: $discount,
            tax: $tax,
            total: self::formatToMoney($document->total, $currencyCode),
            amountDue: $amountDue,
            company: CompanyDTO::fromModel($document->company),
            client: $document->client ? ClientDTO::fromModel($document->client) : null,
            lineItems: self::createLineItems($document),
            label: self::generateDocumentLabels($document),

            lineItemColumns: LineItemColumnsDTO::fromDocumentDefault($settings),
            accentColor: $settings->accent_color ?? '#000000',
            showLogo: $settings->show_logo ?? false,
            font: $settings->font ?? Font::Inter,
            showPaymentDetails: $settings->show_payment_details ?? true,
            paymentDetails: $paymentDetails,
            showAmountInWords: $settings->text_with_amount_in_words ?? false,
            amountInWords: $amountInWords,
            showSignature: $settings->show_signature ?? false,
            signature: $signature,
            showQRCode: $settings->show_qr_code ?? false,
            qrCode: $qrCode,
            showHeader: $settings->show_header ?? true,
            showClientDetails: $settings->show_client_details ?? true,
            showObjetDetails: $settings->show_objet_details ?? true,
            showCompanyDetails: $settings->show_company_details ?? true,
            showLines: $settings->show_lines ?? true,
            showTotals: $settings->show_totals ?? true,
            showDiscount: $settings->show_discount ?? true,
            showTax: $settings->show_tax ?? true,
            showNotes: $settings->show_notes ?? true,
            showFooter: $settings->show_footer ?? true,
        );
    }

    protected static function formatToMoney(int $value, ?string $currencyCode): string
    {
        return CurrencyConverter::formatToMoney($value, $currencyCode);
    }

    protected static function createLineItems(Document $document): \Illuminate\Support\Collection
    {
        // Utiliser le LineItemDTO unifié pour tous les types de documents
        return $document->lineItems->map(fn($item) => LineItemDTO::fromModel($item));
    }

    protected static function generateDocumentLabels(Document $document): DocumentLabelDTO
    {
        return new DocumentLabelDTO(
            title: $document::documentType()->getLabel(),
            number: 'Number',
            referenceNumber: 'Reference',
            date: 'Date',
            dueDate: 'Due Date',
            amountDue: $document::documentType() !== DocumentType::Estimate ? 'Amount Due' : null,
        );
    }

    public function getFontHtml(): Htmlable
    {
        return app(BunnyFontProvider::class)->getHtml($this->font->getLabel());
    }
}
