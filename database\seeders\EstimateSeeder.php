<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\Client;
use App\Models\Estimate;
use App\Models\Offering;
use App\Models\DocumentLineItem;
use App\Models\Adjustment;
use App\Enums\AdjustmentType;
use App\Enums\AdjustmentCategory;
use Illuminate\Database\Seeder;

class EstimateSeeder extends Seeder
{
    public function run(): void
    {
        $companies = Company::all();

        foreach ($companies as $company) {
            $clients = Client::where('company_id', $company->id)->get();

            if ($clients->isNotEmpty()) {
                foreach ($clients->take(2) as $client) {
                    $estimate = $this->createEstimateForClient($company, $client);

                    // Créer les lignes de document polymorphiques
                    $this->createLineItemsForEstimate($estimate);
                }
            }
        }

        $this->command->info('Devis créés avec succès !');
    }

    /**
     * Créer un devis pour un client
     */
    private function createEstimateForClient(Company $company, Client $client): Estimate
    {
        return Estimate::create([
            'company_id' => $company->id,
            'client_id' => $client->id,
            'estimate_number' => 'DEV-' . fake()->unique()->numerify('####'),
            'date' => fake()->dateTimeBetween('-1 month', 'now'),
            'expires_at' => fake()->dateTimeBetween('now', '+2 months'),
            'status' => 'draft',
            'subtotal' => 0, // Sera calculé après les lignes
            'tax_total' => 0,
            'discount_total' => 0,
            'total' => 0,
            'notes' => fake()->optional()->sentence(),
            'created_by' => $company->user_id,
            'updated_by' => $company->user_id,
        ]);
    }

    /**
     * Créer des lignes de document polymorphiques pour un devis
     */
    private function createLineItemsForEstimate(Estimate $estimate): void
    {
        $offerings = Offering::where('company_id', $estimate->company_id)
            ->where('sellable', true)
            ->take(2)
            ->get();

        if ($offerings->isEmpty()) {
            // Créer des lignes génériques pour les devis
            $genericItems = [
                ['name' => 'Étude de faisabilité', 'price' => 500.00],
                ['name' => 'Conception', 'price' => 1200.00],
            ];

            foreach ($genericItems as $index => $item) {
                $lineItem = $estimate->lineItems()->create([
                    'company_id' => $estimate->company_id,
                    'offering_id' => null,
                    'description' => $item['name'],
                    'quantity' => 1,
                    'unit_price' => $item['price'],
                    'tax_total' => 0,
                    'discount_total' => 0,
                    'line_number' => $index + 1,
                    'created_by' => $estimate->created_by,
                    'updated_by' => $estimate->updated_by,
                ]);

                // Attacher les ajustements polymorphiques à la ligne
                $this->attachAdjustmentsToLineItem($lineItem);
            }
            return;
        }

        foreach ($offerings as $index => $offering) {
            $lineItem = $estimate->lineItems()->create([
                'company_id' => $estimate->company_id,
                'offering_id' => $offering->id,
                'description' => $offering->description ?? $offering->name,
                'quantity' => fake()->numberBetween(1, 3),
                'unit_price' => $offering->price,
                'tax_total' => 0,
                'discount_total' => 0,
                'line_number' => $index + 1,
                'created_by' => $estimate->created_by,
                'updated_by' => $estimate->updated_by,
            ]);

            // Attacher les ajustements polymorphiques à la ligne
            $this->attachAdjustmentsToLineItem($lineItem);
        }
    }

    /**
     * Attacher des ajustements polymorphiques à une ligne de document (devis)
     */
    private function attachAdjustmentsToLineItem(DocumentLineItem $lineItem): void
    {
        $adjustments = Adjustment::where('company_id', $lineItem->company_id)->get();

        if ($adjustments->isEmpty()) {
            return;
        }

        // Pour les devis, on peut attacher des taxes mais pas toujours des remises
        $salesTaxes = $adjustments->where('type', AdjustmentType::Sales)
            ->where('category', AdjustmentCategory::Tax)
            ->take(1);

        foreach ($salesTaxes as $tax) {
            $lineItem->adjustments()->attach($tax->id);
        }

        // Remises moins fréquentes pour les devis (20% de chance)
        if (fake()->boolean(20)) {
            $salesDiscounts = $adjustments->where('type', AdjustmentType::Sales)
                ->where('category', AdjustmentCategory::Discount)
                ->take(1);

            foreach ($salesDiscounts as $discount) {
                $lineItem->adjustments()->attach($discount->id);
            }
        }
    }
}
