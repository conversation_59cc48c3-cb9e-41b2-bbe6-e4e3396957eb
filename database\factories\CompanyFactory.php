<?php

namespace Database\Factories;

use App\Models\Company;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Company>
 */
class CompanyFactory extends Factory
{
    protected $model = Company::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->company(),
            'user_id' => User::factory(),
            'personal_company' => false,
        ];
    }

    /**
     * Indicate that the company is a personal company.
     */
    public function personal(): static
    {
        return $this->state(fn(array $attributes) => [
            'personal_company' => true,
        ]);
    }

    /**
     * Configure the model factory.
     */
    public function configure(): static
    {
        return $this->afterCreating(function (Company $company) {
            // Les profils et defaults sont créés par les seeders ou les events/listeners
            // Pas de création automatique ici pour éviter les doublons
        });
    }
}
