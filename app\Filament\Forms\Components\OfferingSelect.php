<?php

namespace App\Filament\Forms\Components;

use App\Filament\Company\Resources\OfferingResource;
use App\Models\Offering;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Support\Enums\MaxWidth;

class OfferingSelect extends Select
{
    protected bool $isPurchasable = true;

    protected bool $isSellable = true;

    public function purchasable(bool $condition = true): static
    {
        $this->isPurchasable = $condition;
        $this->isSellable = false;

        return $this;
    }

    public function sellable(bool $condition = true): static
    {
        $this->isSellable = $condition;
        $this->isPurchasable = false;

        return $this;
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->searchable()
            ->preload()
            ->createOptionForm(fn(Form $form) => $this->createOfferingForm($form))
            ->createOptionAction(fn(Action $action) => $this->createOfferingAction($action));

        $this->relationship('offering', 'name')
            ->getOptionLabelUsing(fn($value): ?string => Offering::find($value)?->name)
            ->getSearchResultsUsing(function (string $search) {
                $query = Offering::where('name', 'like', "%{$search}%");

                if ($this->isSellable() && !$this->isPurchasable()) {
                    $query->where('sellable', true);
                } elseif ($this->isPurchasable() && !$this->isSellable()) {
                    $query->where('purchasable', true);
                }

                return $query->limit(50)->pluck('name', 'id');
            })
            ->getOptionLabelsUsing(function (array $values): array {
                $query = Offering::whereIn('id', $values);

                if ($this->isSellable() && !$this->isPurchasable()) {
                    $query->where('sellable', true);
                } elseif ($this->isPurchasable() && !$this->isSellable()) {
                    $query->where('purchasable', true);
                }

                return $query->pluck('name', 'id')->toArray();
            });

        $this->createOptionUsing(function (array $data, Form $form) {
            if ($this->isSellableAndPurchasable()) {
                $attributes = array_flip($data['attributes'] ?? []);

                $data['sellable'] = isset($attributes['Sellable']);
                $data['purchasable'] = isset($attributes['Purchasable']);
            } else {
                $data['sellable'] = $this->isSellable;
                $data['purchasable'] = $this->isPurchasable;
            }

            unset($data['attributes']);

            $offering = Offering::create($data);

            $form->model($offering)->saveRelationships();

            return $offering->getKey();
        });
    }

    protected function createOfferingForm(Form $form): Form
    {
        return $form->schema([
            OfferingResource::getGeneralSection($this->isSellableAndPurchasable()),
            OfferingResource::getSellableSection()->visible(
                fn(Get $get) => $this->isSellableAndPurchasable()
                ? in_array('Sellable', $get('attributes') ?? [])
                : $this->isSellable()
            ),
            OfferingResource::getPurchasableSection()->visible(
                fn(Get $get) => $this->isSellableAndPurchasable()
                ? in_array('Purchasable', $get('attributes') ?? [])
                : $this->isPurchasable()
            ),
        ]);
    }

    protected function createOfferingAction(Action $action): Action
    {
        return $action
            ->label('Create offering')
            ->slideOver()
            ->modalWidth(MaxWidth::ThreeExtraLarge)
            ->modalHeading('Create a new offering');
    }

    public function isSellable(): bool
    {
        return $this->isSellable;
    }

    public function isPurchasable(): bool
    {
        return $this->isPurchasable;
    }

    public function isSellableAndPurchasable(): bool
    {
        return $this->isSellable && $this->isPurchasable;
    }
}
