@props([
    'accentColor',
    'amountDue',
    'titleTag' => 'h4',
    'titleClass' => 'text-left text-sm font-normal mx-2 italic',
    'paymentDetails' => null,
    'headerTextColor' => 'text-white',
])

<div {{ $attributes->class(['doc-template-payment-breakdown py-4']) }}>
    @if ($titleTag === 'p')
        <p class="{{ $titleClass }} py-2">---REPARTITION DES PAIMENTS---</p>
    @else
        <h4 class="{{ $titleClass }} py-2">---REPARTITION DES PAIMENTS---</h4>
    @endif

    <table class="w-full table-fixed text-left">
        <thead class="text-sm leading-relaxed" style="background: {{ $accentColor }}">
            <tr class="{{ $headerTextColor }}">
                <td class="text-left pl-6 w-[50%] py-2">Type de paiement</td>
                <td class="text-left pr-6 w-[50%] py-2">{{ $paymentDetails?->status ?? 'PAEER' }}</td>
            </tr>
        </thead>
        <tbody class="text-sm border-gray-300 border-b">
            <tr class="">
                <td class="text-left pl-6 font-semibold py-3">{{ $paymentDetails?->paymentType ?? 'ESPECES' }}</td>
                <td class="text-right pr-6 font-semibold py-3">{{ $paymentDetails?->amount ?? $amountDue }}</td>
            </tr>
        </tbody>
    </table>
</div>
