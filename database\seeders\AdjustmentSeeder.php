<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\Adjustment;
use App\Enums\AdjustmentType;
use App\Enums\AdjustmentCategory;
use Illuminate\Database\Seeder;

class AdjustmentSeeder extends Seeder
{
    public function run(): void
    {
        $companies = Company::all();

        if ($companies->isEmpty()) {
            $this->command->warn('Aucune entreprise trouvée. Exécutez CompanySeeder en premier.');
            return;
        }

        foreach ($companies as $company) {
            $this->createAdjustmentsForCompany($company);
        }

        $this->command->info('Ajustements créés avec succès !');
    }

    private function createAdjustmentsForCompany(Company $company): void
    {
        $adjustments = [
            [
                'name' => 'TVA 20%',
                'description' => 'Taxe sur la valeur ajoutée 20%',
                'type' => AdjustmentType::Sales,
                'category' => AdjustmentCategory::Tax,
                'rate' => 20.00,
                'status' => 'active',
                'computation' => 'percentage',
            ],
            [
                'name' => 'TVA 10%',
                'description' => 'Taxe sur la valeur ajoutée 10%',
                'type' => AdjustmentType::Sales,
                'category' => AdjustmentCategory::Tax,
                'rate' => 10.00,
                'status' => 'active',
                'computation' => 'percentage',
            ],
            [
                'name' => 'Remise 5%',
                'description' => 'Remise commerciale 5%',
                'type' => AdjustmentType::Sales,
                'category' => AdjustmentCategory::Discount,
                'rate' => 5.00,
                'status' => 'active',
                'computation' => 'percentage',
            ],
            [
                'name' => 'TVA Achat 20%',
                'description' => 'TVA déductible sur achats',
                'type' => AdjustmentType::Purchase,
                'category' => AdjustmentCategory::Tax,
                'rate' => 20.00,
                'status' => 'active',
                'computation' => 'percentage',
            ],
        ];

        foreach ($adjustments as $adjustmentData) {
            Adjustment::create(array_merge($adjustmentData, [
                'company_id' => $company->id,
                'created_by' => $company->user_id,
                'updated_by' => $company->user_id,
            ]));
        }
    }
}
