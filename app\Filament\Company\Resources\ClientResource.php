<?php

namespace App\Filament\Company\Resources;

use App\Enums\ClientType;
use App\Filament\Company\Resources\ClientResource\Pages;
use App\Models\Client;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use JaOcero\RadioDeck\Forms\Components\RadioDeck;

class ClientResource extends Resource
{
    protected static ?string $model = Client::class;


    //protected static ?string $navigationIcon = 'heroicon-o-users';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                RadioDeck::make('type')
                    ->label('Client Type')
                    ->options(ClientType::class)
                    ->default(ClientType::Individual)
                    ->icons(ClientType::class)
                    ->color('primary')
                    ->columns(4)
                    ->required()
                    ->columnSpanFull(),

                Forms\Components\TextInput::make('name')
                    ->label('Client Name')
                    ->required()
                    ->maxLength(255)
                    ->placeholder('Enter client name'),

                Forms\Components\TextInput::make('fiscal_number')
                    ->label('Fiscal Number')
                    ->maxLength(255)
                    ->placeholder('Enter fiscal/tax number'),

                Forms\Components\TextInput::make('contact')
                    ->label('Contact Information')
                    ->placeholder('Enter contact details'),

                Forms\Components\Textarea::make('address')
                    ->label('Address')
                    ->placeholder('Enter client address')
                    ->rows(3)
                    ->columnSpanFull(),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Client Name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('type')
                    ->label('Type')
                    ->badge()
                    ->sortable(),

                Tables\Columns\TextColumn::make('fiscal_number')
                    ->label('Fiscal Number')
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('contact')
                    ->label('Contact')
                    ->searchable()
                    ->limit(30)
                    ->toggleable(),

                Tables\Columns\TextColumn::make('address')
                    ->label('Address')
                    ->searchable()
                    ->limit(40)
                    ->toggleable(),

                Tables\Columns\TextColumn::make('invoices_count')
                    ->label('Invoices')
                    ->counts('invoices')
                    ->sortable()
                    ->toggleable(),

                /* Tables\Columns\TextColumn::make('estimates_count')
                    ->label('Estimates')
                    ->counts('estimates')
                    ->sortable()
                    ->toggleable(), */

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Updated')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label('Client Type')
                    ->options(ClientType::class)
                    ->default(ClientType::Individual->value)
                    ->multiple(),

                Tables\Filters\Filter::make('has_fiscal_number')
                    ->label('Has Fiscal Number')
                    ->query(fn(Builder $query): Builder => $query->whereNotNull('fiscal_number')),

                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('Created from'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Created until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->filtersTriggerAction(
                fn(Tables\Actions\Action $action) => $action
                    ->button()
                    ->label('Filters')
                    ->icon('heroicon-m-funnel')
                    ->slideOver()
            )
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListClients::route('/'),
            'create' => Pages\CreateClient::route('/create'),
            'view' => Pages\ViewClient::route('/{record}'),
            'edit' => Pages\EditClient::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    public static function getNavigationGroup(): ?string
    {
        return 'Customer Management';
    }

    protected static ?int $navigationSort = 1;
}
