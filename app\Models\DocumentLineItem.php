<?php

namespace App\Models;

use App\Traits\Blamable;
use Akaunting\Money\Money;
use App\Traits\CompanyOwned;
use App\Enums\AdjustmentType;
use App\Enums\AdjustmentCategory;
use App\Utilities\CurrencyAccessor;
use App\Utilities\RateCalculator;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

class DocumentLineItem extends Model
{
    use HasFactory;
    use Blamable;
    use CompanyOwned;
    use HasFactory;

    protected $table = 'document_line_items';

    protected $fillable = [
        'company_id',
        'offering_id',
        'description',
        'quantity',
        'unit_price',
        'tax_total',
        'discount_total',
        'line_number',
        'delivery_person',
        'receiver_person',
        'delivery_remarks',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'unit_price' => 'decimal:2',
        'tax_total' => 'decimal:2',
        'discount_total' => 'decimal:2',
    ];

    public function documentable(): MorphTo
    {
        return $this->morphTo();
    }

    public function offering(): BelongsTo
    {
        return $this->belongsTo(Offering::class);
    }

    public function sellableOffering(): BelongsTo
    {
        return $this->offering()->where('sellable', true);
    }

    public function purchasableOffering(): BelongsTo
    {
        return $this->offering()->where('purchasable', true);
    }

    public function adjustments(): MorphToMany
    {
        return $this->morphToMany(Adjustment::class, 'adjustmentable', 'adjustmentables');
    }

    public function salesTaxes(): MorphToMany
    {
        return $this->adjustments()->where('category', AdjustmentCategory::Tax)->where('type', AdjustmentType::Sales);
    }

    public function purchaseTaxes(): MorphToMany
    {
        return $this->adjustments()->where('category', AdjustmentCategory::Tax)->where('type', AdjustmentType::Purchase);
    }

    public function salesDiscounts(): MorphToMany
    {
        return $this->adjustments()->where('category', AdjustmentCategory::Discount)->where('type', AdjustmentType::Sales);
    }

    public function purchaseDiscounts(): MorphToMany
    {
        return $this->adjustments()->where('category', AdjustmentCategory::Discount)->where('type', AdjustmentType::Purchase);
    }

    public function taxes(): MorphToMany
    {
        return $this->adjustments()->where('category', AdjustmentCategory::Tax);
    }

    public function discounts(): MorphToMany
    {
        return $this->adjustments()->where('category', AdjustmentCategory::Discount);
    }

    public function calculateTaxTotal(): Money
    {
        $subtotal = $this->quantity * $this->unit_price;
        $defaultCurrency = CurrencyAccessor::getDefaultCurrency();

        return $this->taxes->reduce(function (Money $carry, Adjustment $tax) use ($subtotal, $defaultCurrency) {
            if ($tax->computation->isPercentage()) {
                $taxAmount = $subtotal * ($tax->rate / 100);
                return $carry->add(money($taxAmount, $defaultCurrency, true));
            } else {
                return $carry->add(money($tax->rate, $defaultCurrency, true));
            }
        }, money(0, $defaultCurrency));
    }

    public function calculateTaxTotalAmount(): float
    {
        $subtotal = $this->quantity * $this->unit_price;

        return $this->taxes->reduce(function (float $carry, Adjustment $tax) use ($subtotal) {
            if ($tax->computation->isPercentage()) {
                $scaledRate = RateCalculator::decimalToScaledRate($tax->rate);
                $taxAmount = RateCalculator::scaledRateToDecimal(
                    RateCalculator::calculatePercentage(
                        RateCalculator::decimalToScaledRate($subtotal),
                        $scaledRate
                    )
                );
                return $carry + $taxAmount;
            } else {
                return $carry + $tax->rate;
            }
        }, 0.0);
    }

    public function calculateDiscountTotal(): Money
    {
        $subtotal = $this->quantity * $this->unit_price;
        $defaultCurrency = CurrencyAccessor::getDefaultCurrency();

        return $this->discounts->reduce(function (Money $carry, Adjustment $discount) use ($subtotal, $defaultCurrency) {
            if ($discount->computation->isPercentage()) {
                $discountAmount = $subtotal * $discount->rate / 100;
                return $carry->add(money($discountAmount, $defaultCurrency, true));
            } else {
                return $carry->add(money($discount->rate, $defaultCurrency, true));
            }
        }, money(0, $defaultCurrency));
    }

    public function calculateDiscountTotalAmount(): float
    {
        $subtotal = $this->quantity * $this->unit_price;

        return $this->discounts->reduce(function (float $carry, Adjustment $discount) use ($subtotal) {
            if ($discount->computation->isPercentage()) {
                $scaledRate = RateCalculator::decimalToScaledRate($discount->rate);
                $discountAmount = RateCalculator::scaledRateToDecimal(
                    RateCalculator::calculatePercentage(
                        RateCalculator::decimalToScaledRate($subtotal),
                        $scaledRate
                    )
                );
                return $carry + $discountAmount;
            } else {
                return $carry + $discount->rate;
            }
        }, 0.0);
    }

    public function calculateAdjustmentTotal(Adjustment $adjustment): Money
    {
        $subtotal = money($this->subtotal, CurrencyAccessor::getDefaultCurrency());

        return $subtotal->multiply($adjustment->rate / 100);
    }

    public function calculateAdjustmentTotalAmount(Adjustment $adjustment): float
    {
        $subtotal = $this->quantity * $this->unit_price;

        if ($adjustment->computation->isPercentage()) {
            $scaledRate = RateCalculator::decimalToScaledRate($adjustment->rate);
            return RateCalculator::scaledRateToDecimal(
                RateCalculator::calculatePercentage(
                    RateCalculator::decimalToScaledRate($subtotal),
                    $scaledRate
                )
            );
        } else {
            return (float) $adjustment->rate;
        }
    }
}
