<?php

namespace App\Enums;

use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum DocumentType: string implements HasIcon, HasLabel
{
    case Invoice = 'invoice';
    case Delivery = 'delivery';
    case Estimate = 'estimate';
    case Bill = 'bill';

    public const DEFAULT = self::Invoice->value;

    public function getLabel(): ?string
    {
        $label = match ($this) {
            self::Invoice => 'Inv',
            self::Delivery => 'Del',
            self::Estimate => 'Est',
            self::Bill => 'Bill',
        };

        return $label;
    }

    public function getIcon(): ?string
    {
        return match ($this->value) {
            self::Invoice->value => 'heroicon-o-document-currency-dollar',
            self::Delivery->value => 'heroicon-o-truck',
            self::Estimate->value => 'heroicon-o-calculator',
            self::Bill->value => 'heroicon-o-receipt-percent',
        };
    }

    public function Prefix(): string
    {
        return match ($this) {
            self::Invoice => 'INV-',
            self::Delivery => 'DELV-',
            self::Estimate => 'EST-',
            self::Bill => 'BILL-',
        };
    }

    public function Header(): string
    {
        return match ($this) {
            self::Invoice => 'Inv',
            self::Delivery => 'Del',
            self::Estimate => 'Est',
            self::Bill => 'Bill',
        };
    }
}
