<?php

namespace App\DTO;

use Illuminate\Support\Number;

readonly class AmountInWordsDTO
{
    public function __construct(
        public string $amountInWords,
        public string $formattedText,
    ) {
    }

    public static function fromAmount(string $amount, string $currencyCode = 'EUR', string $locale = 'fr'): self
    {
        // Nettoyer le montant pour ne garder que les chiffres et le point décimal
        $cleanAmount = preg_replace('/[^\d.]/', '', $amount);
        $numericAmount = (float) $cleanAmount;

        // Convertir le montant en mots avec Number::spell de Laravel
        $numberToWords = Number::spell($numericAmount, $locale);

        // Créer le texte formaté
        $formattedText = "Arrêté la présente facture à la somme de {$numberToWords} {$currencyCode} TTC.";

        return new self(
            amountInWords: $numberToWords,
            formattedText: $formattedText,
        );
    }

    public static function fake(): self
    {
        return new self(
            amountInWords: 'neuf cent cinquante',
            formattedText: 'Arrêté la présente facture à la somme de neuf cent cinquante EUR TTC.',
        );
    }

    public function toArray(): array
    {
        return [
            'amount_in_words' => $this->amountInWords,
            'formatted_text' => $this->formattedText,
        ];
    }
}
