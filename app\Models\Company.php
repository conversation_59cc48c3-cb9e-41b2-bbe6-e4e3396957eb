<?php

namespace App\Models;

use App\Enums\DocumentType;
use Filament\Models\Contracts\HasAvatar;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Wallo\FilamentCompanies\Events\CompanyCreated;
use Wallo\FilamentCompanies\Events\CompanyDeleted;
use Wallo\FilamentCompanies\Events\CompanyUpdated;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Wallo\FilamentCompanies\Company as FilamentCompaniesCompany;

class Company extends FilamentCompaniesCompany implements HasAvatar
{
    use HasFactory;

    protected $fillable = [
        'name',
        'personal_company',
    ];

    protected $dispatchesEvents = [
        'created' => CompanyCreated::class,
        'updated' => CompanyUpdated::class,
        'deleted' => CompanyDeleted::class,
    ];

    protected function casts(): array
    {
        return [
            'personal_company' => 'boolean',
        ];
    }

    public function getFilamentAvatarUrl(): string
    {
        return $this->owner->profile_photo_url;
    }

    public function adjustments(): HasMany
    {
        return $this->hasMany(Adjustment::class, 'company_id');
    }

    public function currencies(): HasMany
    {
        return $this->hasMany(Currency::class, 'company_id');
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class, 'company_id');
    }

    public function clients(): HasMany
    {
        return $this->hasMany(Client::class, 'company_id');
    }

    public function contacts(): HasMany
    {
        return $this->hasMany(Contact::class, 'company_id');
    }

    public function offerings(): HasMany
    {
        return $this->hasMany(Offering::class, 'company_id');
    }

    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class, 'company_id');
    }

    public function estimates(): HasMany
    {
        return $this->hasMany(Estimate::class, 'company_id');
    }

    public function deliveries(): HasMany
    {
        return $this->hasMany(Delivery::class, 'company_id');
    }

    public function documentDefaults(): HasMany
    {
        return $this->hasMany(DocumentDefault::class, 'company_id');
    }

    public function defaultEstimate(): HasOne
    {
        return $this->hasOne(DocumentDefault::class, 'company_id')
            ->where('type', DocumentType::Estimate);
    }

    public function defaultInvoice(): HasOne
    {
        return $this->hasOne(DocumentDefault::class, 'company_id')
            ->where('type', DocumentType::Invoice);
    }

    public function defaultDelivery(): HasOne
    {
        return $this->hasOne(DocumentDefault::class, 'company_id')
            ->where('type', DocumentType::Delivery);
    }

    public function departments(): HasMany
    {
        return $this->hasMany(Department::class, 'company_id');
    }

    public function locale(): HasOne
    {
        return $this->hasOne(Localization::class, 'company_id');
    }

    public function profile(): HasOne
    {
        return $this->hasOne(CompanyProfile::class, 'company_id');
    }

    public function defaults(): HasOne
    {
        return $this->hasOne(CompanyDefault::class, 'company_id');
    }
}
