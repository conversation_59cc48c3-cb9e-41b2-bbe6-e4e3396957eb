<?php

namespace Database\Seeders;

use App\Models\Currency;
use App\Models\Company;
use Illuminate\Database\Seeder;

class CurrencySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $companies = Company::all();

        if ($companies->isEmpty()) {
            $this->command->warn('Aucune entreprise trouvée. Exécutez CompanySeeder en premier.');
            return;
        }

        $currencies = [
            [
                'name' => 'Euro',
                'code' => 'EUR',
                'rate' => 1.0000,
                'precision' => 2,
                'symbol' => '€',
                'symbol_first' => false,
                'decimal_mark' => ',',
                'thousands_separator' => ' ',
                'enabled' => true,
            ],
            [
                'name' => 'US Dollar',
                'code' => 'USD',
                'rate' => 1.0800,
                'precision' => 2,
                'symbol' => '$',
                'symbol_first' => true,
                'decimal_mark' => '.',
                'thousands_separator' => ',',
                'enabled' => true,
            ],
            [
                'name' => 'Franc CFA (BCEAO)',
                'code' => 'XOF',
                'rate' => 655.9570,
                'precision' => 0,
                'symbol' => 'CFA',
                'symbol_first' => false,
                'decimal_mark' => ',',
                'thousands_separator' => ' ',
                'enabled' => true,
            ],
        ];

        // Créer les devises pour chaque entreprise
        foreach ($companies as $company) {
            foreach ($currencies as $currencyData) {
                // Vérifier si la devise existe déjà pour cette entreprise
                $existing = Currency::where('company_id', $company->id)
                    ->where('code', $currencyData['code'])
                    ->first();

                if (!$existing) {
                    Currency::create(array_merge($currencyData, [
                        'company_id' => $company->id,
                        'created_by' => $company->user_id,
                        'updated_by' => $company->user_id,
                    ]));
                }
            }
        }

        $this->command->info('Devises créées avec succès pour toutes les entreprises !');
    }
}
