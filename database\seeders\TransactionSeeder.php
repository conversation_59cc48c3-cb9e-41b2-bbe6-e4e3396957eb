<?php

namespace Database\Seeders;

use App\Models\Invoice;
use App\Models\Estimate;
use App\Models\Transaction;
use App\Enums\TransactionType;
use App\Enums\PaymentMethod;
use Illuminate\Database\Seeder;

class TransactionSeeder extends Seeder
{
    public function run(): void
    {
        // Transactions pour les factures
        $invoices = Invoice::all();
        foreach ($invoices as $invoice) {
            $transactionCount = rand(1, 2);

            for ($i = 0; $i < $transactionCount; $i++) {
                $this->createTransactionForDocument($invoice);
            }
        }

        // Transactions pour les devis (moins fréquentes)
        $estimates = Estimate::all();
        foreach ($estimates->take(ceil($estimates->count() / 2)) as $estimate) {
            $this->createTransactionForDocument($estimate);
        }

        $this->command->info('Transactions créées avec succès !');
        $this->command->info("   • Factures: {$invoices->count()} avec transactions");
        $this->command->info("   • Devis: " . ceil($estimates->count() / 2) . " avec transactions");
    }

    /**
     * Créer une transaction pour un document (Invoice ou Estimate)
     */
    private function createTransactionForDocument($document): void
    {
        $paymentMethods = [
            PaymentMethod::Transfer,
            PaymentMethod::Cash,
            PaymentMethod::Other,
        ];

        Transaction::create([
            'company_id' => $document->company_id,
            'transactionable_type' => get_class($document),
            'transactionable_id' => $document->id,
            'type' => TransactionType::Deposit,
            'payment_method' => fake()->randomElement($paymentMethods),
            'amount' => fake()->randomFloat(2, 50, 1000),
            'date' => fake()->dateTimeBetween('-2 months', 'now'),
            'description' => 'Paiement ' . class_basename($document) . ' #' . $document->number,
            'created_by' => $document->created_by,
            'updated_by' => $document->updated_by,
        ]);
    }
}
