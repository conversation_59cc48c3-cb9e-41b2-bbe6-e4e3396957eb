<?php

namespace App\Traits;

use App\Scopes\CurrentCompanyScope;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Wallo\FilamentCompanies\FilamentCompanies;

trait CompanyOwned
{
    public static function bootCompanyOwned(): void
    {
        static::creating(static function ($model) {
            if (empty($model->company_id)) {
                $companyId = session('current_company_id');

                if (!$companyId && Auth::check() && Auth::user()->currentCompany) {
                    $companyId = Auth::user()->currentCompany->id;
                    session(['current_company_id' => $companyId]);
                }

                if ($companyId) {
                    $model->company_id = $companyId;
                } else {
                    // Si aucune entreprise courante n'est définie, on peut soit :
                    // 1. Laisser company_id vide (null) - le modèle sera créé sans entreprise
                    // 2. Ou empêcher la création en lançant une exception
                    // Pour l'instant, on log un warning et on laisse company_id à null
                    Log::warning('CompanyOwned: No company_id found for user ' . Auth::id() . ' when creating ' . get_class($model));
                }
            }
        });

        static::addGlobalScope(new CurrentCompanyScope);
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(FilamentCompanies::companyModel(), 'company_id');
    }
}
