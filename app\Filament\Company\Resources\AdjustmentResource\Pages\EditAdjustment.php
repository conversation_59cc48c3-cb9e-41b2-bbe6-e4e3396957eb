<?php

namespace App\Filament\Company\Resources\AdjustmentResource\Pages;

use Filament\Actions;
use App\Traits\HandlePageRedirect;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Company\Resources\AdjustmentResource;

class EditAdjustment extends EditRecord
{
    use HandlePageRedirect;

    protected static string $resource = AdjustmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
