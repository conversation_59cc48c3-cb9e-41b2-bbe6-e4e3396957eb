<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\User;
use Illuminate\Database\Seeder;

class CompanySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Récupérer les utilisateurs existants
        $users = User::all();

        if ($users->isEmpty()) {
            $this->command->warn('Aucun utilisateur trouvé. Exécutez UserSeeder en premier.');
            return;
        }

        // Entreprises d'exemple
        $companies = [
            [
                'name' => 'TechCorp Solutions',
                'personal_company' => false,
                'user_id' => $users->first()->id,
            ],
            [
                'name' => 'Digital Innovations SARL',
                'personal_company' => false,
                'user_id' => $users->skip(1)->first()?->id ?? $users->first()->id,
            ],
            [
                'name' => 'Consulting Pro',
                'personal_company' => false,
                'user_id' => $users->skip(2)->first()?->id ?? $users->first()->id,
            ],
        ];

        foreach ($companies as $companyData) {
            Company::create($companyData);
        }

        // Entreprises supplémentaires avec factory
        Company::factory(3)->create();

        $this->command->info('Entreprises créées avec succès !');
    }
}
