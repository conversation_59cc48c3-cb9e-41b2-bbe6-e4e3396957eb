<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DemoSeeder extends Seeder
{
    /**
     * Seeder pour créer des données de démonstration complètes
     * Utilise les factories et seeders dans le bon ordre
     */
    public function run(): void
    {
        $this->command->info('🚀 Début du seeding des données de démonstration...');

        // Désactiver les contraintes de clés étrangères temporairement
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        try {
            // 1. Utilisateurs et entreprises
            $this->command->info('👥 Création des utilisateurs et entreprises...');
            $this->call([
                UserSeeder::class,
                CompanySeeder::class,
            ]);

            // 2. Données de référence (dépendent des entreprises)
            $this->command->info('📊 Création des données de référence...');
            $this->call([
                CurrencySeeder::class,
                LocalizationSeeder::class,
            ]);

            // 3. Profils et paramètres
            $this->command->info('⚙️ Configuration des profils et paramètres...');
            $this->call([
                CompanyProfileSeeder::class,
                DocumentDefaultSeeder::class,
            ]);

            // 4. Structure organisationnelle
            $this->command->info('🏢 Création de la structure organisationnelle...');
            $this->call([
                DepartmentSeeder::class,
            ]);

            // 5. Catalogue produits/services avec ajustements polymorphiques
            $this->command->info('📦 Création du catalogue produits/services...');
            $this->call([
                AdjustmentSeeder::class,
                OfferingSeeder::class,
            ]);

            // 6. Clients
            $this->command->info('🏠 Création des clients...');
            $this->call([
                ClientSeeder::class,
            ]);

            // 7. Documents commerciaux
            $this->command->info('📄 Création des documents commerciaux...');
            $this->call([
                InvoiceSeeder::class,
                EstimateSeeder::class,
            ]);

            // 8. Transactions
            $this->command->info('💰 Création des transactions...');
            $this->call([
                TransactionSeeder::class,
            ]);

        } finally {
            // Réactiver les contraintes de clés étrangères
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        }

        $this->command->info('✅ Seeding terminé avec succès !');
        $this->command->info('');
        $this->command->info('📋 Résumé des données créées :');
        $this->printSummary();
    }

    private function printSummary(): void
    {
        $models = [
            'Utilisateurs' => \App\Models\User::class,
            'Entreprises' => \App\Models\Company::class,
            'Profils d\'entreprise' => \App\Models\CompanyProfile::class,
            'Paramètres par défaut' => \App\Models\DocumentDefault::class,
            'Départements' => \App\Models\Department::class,
            'Contacts' => \App\Models\Contact::class,
            'Produits/Services' => \App\Models\Offering::class,
            'Clients' => \App\Models\Client::class,
            'Adresses' => \App\Models\Address::class,
            'Factures' => \App\Models\Invoice::class,
            'Devis' => \App\Models\Estimate::class,
            'Transactions' => \App\Models\Transaction::class,
        ];

        foreach ($models as $name => $model) {
            $count = $model::count();
            $this->command->info("   • {$name}: {$count}");
        }
    }
}
