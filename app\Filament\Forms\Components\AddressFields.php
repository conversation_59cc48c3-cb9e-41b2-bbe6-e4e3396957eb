<?php

namespace App\Filament\Forms\Components;

use Closure;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;

class AddressFields extends Grid
{
    protected bool|Closure $isCountryDisabled = false;
    protected bool|Closure $isRequired = false;

    protected function setUp(): void
    {
        parent::setUp();

        $this->schema([
            TextInput::make('address_line_1')
                ->label('Address line 1')
                ->required(fn() => $this->isRequired())
                ->maxLength(255),

            TextInput::make('address_line_2')
                ->label('Address line 2')
                ->maxLength(255),

            CountrySelect::make('country_code')
                ->disabled(fn() => $this->isCountryDisabled())
                ->required(fn() => $this->isRequired())
                ->clearStateField(),

            StateSelect::make('state_id'),

            TextInput::make('city')
                ->label('City')
                ->required(fn() => $this->isRequired())
                ->maxLength(255),

            TextInput::make('postal_code')
                ->label('Postal code')
                ->maxLength(255),
        ]);
    }

    /**
     * Active ou désactive la validation "required" sur les champs.
     */
    public function required(bool|Closure $condition = true): static
    {
        $this->isRequired = $condition;

        return $this;
    }

    public function isRequired(): bool
    {
        return (bool) $this->evaluate($this->isRequired);
    }

    /**
     * Active ou désactive le champ "country_code".
     */
    public function disabledCountry(bool|Closure $condition = true): static
    {
        $this->isCountryDisabled = $condition;

        return $this;
    }

    public function isCountryDisabled(): bool
    {
        return (bool) $this->evaluate($this->isCountryDisabled);
    }
}
