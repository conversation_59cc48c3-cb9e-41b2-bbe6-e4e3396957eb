<?php

namespace App\Filament\Company\Pages;


use App\Models\Company;
use App\Models\Country;
use Filament\Forms\Form;
use App\Enums\EntityType;
use App\Enums\AddressType;
use App\Enums\DateFormat;
use App\Enums\TimeFormat;
use App\Enums\WeekStart;
use App\Enums\NumberFormat;
use App\Utilities\Timezone;
use App\Utilities\CurrencyAccessor;
use Illuminate\Support\Facades\DB;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Filament\Forms\Components\Select;

use Filament\Forms\Components\Wizard;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\TextInput;
use Illuminate\Contracts\Support\Htmlable;
use Wallo\FilamentCompanies\FilamentCompanies;
use Wallo\FilamentCompanies\Events\AddingCompany;
use Wallo\FilamentCompanies\Pages\Company\CreateCompany as FilamentCreateCompany;

class CreateCompany extends FilamentCreateCompany
{
    protected bool $hasTopbar = false;

    protected static string $view = 'filament.company.pages.create-company';

    protected static string $layout = 'components.company.layout.custom-simple';

    public function getHeading(): string|Htmlable
    {
        return 'ERPSAAS';
    }

    public function getMaxWidth(): MaxWidth|string|null
    {
        return MaxWidth::SevenExtraLarge;
    }

    public function hasLogo(): bool
    {
        return true;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Wizard::make([
                    Wizard\Step::make('Informations générales')
                        ->schema([
                            TextInput::make('name')
                                ->label('Nom de l\'entreprise')
                                ->autofocus()
                                ->maxLength(255)
                                ->required()
                                ->columnSpanFull(),

                            TextInput::make('profile.email')
                                ->label('Email principal')
                                ->email()
                                ->columnSpanFull()
                                ->required(),

                            Select::make('profile.entity_type')
                                ->label('Type d\'entité')
                                ->options(EntityType::class)
                                ->columnSpanFull()
                                ->required(),

                            Select::make('profile.currency_code')
                                ->label('Devise principale')
                                ->searchable()
                                ->options(CurrencyAccessor::getAllCurrencyOptions())
                                ->optionsLimit(10)
                                ->required()
                                ->columnSpanFull(),
                        ])
                        ->columns(2),

                    Wizard\Step::make('Localisation')
                        ->schema([
                            Select::make('address.country_code')
                                ->label('Pays')
                                ->live()
                                ->searchable()
                                ->options(Country::getAvailableCountryOptions())
                                ->getSearchResultsUsing(fn(string $search): array => Country::getSearchResultsUsing($search))
                                ->getOptionLabelUsing(fn($value): ?string => Country::find($value)?->name . ' ' . Country::find($value)?->flag)
                                ->afterStateUpdated(function ($set) {
                                    $set('locale.timezone', null);
                                    $set('locale.language', null);
                                })
                                ->required()
                                ->columnSpanFull(),

                            Select::make('address.state_id')
                                ->label('État/Province')
                                ->searchable()
                                ->options(function ($get) {
                                    $countryCode = $get('address.country_code');
                                    if (!$countryCode) {
                                        return [];
                                    }
                                    return Country::find($countryCode)?->states()->pluck('name', 'id')->toArray() ?? [];
                                })
                                ->columnSpanFull(),

                            Select::make('locale.language')
                                ->label('Langue')
                                ->searchable()
                                ->options(function ($get) {
                                    $countryCode = $get('address.country_code');
                                    return Country::getLanguagesByCountryCode($countryCode);
                                })
                                ->default('fr')
                                ->columnSpanFull()
                                ->required(),

                            Select::make('locale.timezone')
                                ->label('Fuseau horaire')
                                ->searchable()
                                ->options(function ($get) {
                                    $countryCode = $get('address.country_code');
                                    return Timezone::getTimezoneOptions($countryCode);
                                })
                                ->required()
                                ->columnSpanFull(),

                            Select::make('locale.date_format')
                                ->label('Format de date')
                                ->options(DateFormat::class)
                                ->default(DateFormat::DMY_SLASH)
                                ->columnSpanFull()
                                ->required(),
                        ])
                        ->columns(2),
                ])
                    ->columnSpanFull(),
            ])
            ->columns()
            ->model(FilamentCompanies::companyModel())
            ->statePath('data');
    }

    protected function handleRegistration(array $data): Model
    {
        $user = Auth::user();

        Gate::forUser($user)->authorize('create', FilamentCompanies::newCompanyModel());

        AddingCompany::dispatch($user);

        $personalCompany = $user?->personalCompany() === null;

        return DB::transaction(function () use ($user, $data, $personalCompany) {
            /** @var Company $company */
            $company = $user?->ownedCompanies()->create([
                'name' => $data['name'],
                'personal_company' => $personalCompany,
            ]);

            // Créer le profil de l'entreprise avec les champs essentiels
            $profile = $company->profile()->create([
                'email' => $data['profile']['email'],
                'entity_type' => $data['profile']['entity_type'],
                'currency_code' => $data['profile']['currency_code'],
                // Valeurs par défaut pour les champs obligatoires
                'rccm' => 'TBD',
                'fiscal_number' => 'TBD',
                'siret_number' => 'TBD',
            ]);

            // Créer l'adresse de base
            $addressData = [
                'company_id' => $company->id,
                'type' => AddressType::General,
                'country_code' => $data['address']['country_code'],
            ];

            // Ajouter state_id si fourni
            if (!empty($data['address']['state_id'])) {
                $addressData['state_id'] = $data['address']['state_id'];
            }

            $profile->address()->create($addressData);

            // Créer la localisation avec les paramètres essentiels
            $company->locale()->create([
                'language' => $data['locale']['language'],
                'timezone' => $data['locale']['timezone'],
                'date_format' => $data['locale']['date_format'],
                // Valeurs par défaut pour les autres champs obligatoires
                'time_format' => TimeFormat::G12_CAP,
                'fiscal_year_end_month' => 12,
                'fiscal_year_end_day' => 31,
                'week_start' => WeekStart::Monday,
                'number_format' => NumberFormat::CommaDot,
                'percent_first' => false,
            ]);

            // Basculer vers la nouvelle entreprise
            $user?->switchCompany($company);

            // Les paramètres par défaut seront créés automatiquement par les événements
            // CompanyCreated -> InitializeCompanyConfiguration -> CompanyDefaultSyncService

            // Notifier la création réussie
            $this->companyCreated($data['name']);

            return $company;
        });

    }
}
