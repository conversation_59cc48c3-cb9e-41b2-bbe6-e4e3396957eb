<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\Client;
use App\Models\Address;
use App\Models\Contact;
use App\Enums\ClientType;
use App\Enums\AddressType;
use App\Enums\ContactType;
use Illuminate\Database\Seeder;

class ClientSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $companies = Company::all();

        if ($companies->isEmpty()) {
            $this->command->warn('Aucune entreprise trouvée. Exécutez CompanySeeder en premier.');
            return;
        }

        foreach ($companies->take(3) as $company) {
            $this->createClientsForCompany($company);
        }

        // Clients supplémentaires pour les autres entreprises
        $remainingCompanies = $companies->skip(3);
        foreach ($remainingCompanies as $company) {
            $this->createClientsForCompany($company);
        }

        $this->command->info('Clients créés avec succès !');
    }

    private function createClientsForCompany(Company $company): void
    {
        $clients = [
            [
                'name' => 'ACME Corporation',
                'fiscal_number' => 'FR12345678901',
                'address' => '123 Rue de la Paix, 75001 Paris, France',
                'contact' => '<EMAIL> - +33 1 23 45 67 89',
                'type' => ClientType::Business,
            ],
            [
                'name' => 'Jean Dupont',
                'fiscal_number' => null,
                'address' => '456 Avenue des Champs, 69000 Lyon, France',
                'contact' => '<EMAIL> - +33 4 12 34 56 78',
                'type' => ClientType::Individual,
            ],
            [
                'name' => 'TechStart SARL',
                'fiscal_number' => 'FR98765432109',
                'address' => '789 Boulevard Innovation, 31000 Toulouse, France',
                'contact' => '<EMAIL> - +33 5 87 65 43 21',
                'type' => ClientType::Business,
            ],
            [
                'name' => 'Marie Martin',
                'fiscal_number' => null,
                'address' => '321 Rue du Commerce, 13000 Marseille, France',
                'contact' => '<EMAIL> - +33 4 91 23 45 67',
                'type' => ClientType::Individual,
            ],
            [
                'name' => 'Global Solutions SA',
                'fiscal_number' => 'FR11111111111',
                'address' => '654 Place de la République, 67000 Strasbourg, France',
                'contact' => '<EMAIL> - +33 3 88 12 34 56',
                'type' => ClientType::Business,
            ],
        ];

        foreach ($clients as $clientData) {
            Client::create(array_merge($clientData, [
                'company_id' => $company->id,
                'created_by' => $company->user_id,
                'updated_by' => $company->user_id,
            ]));
        }
    }
}
