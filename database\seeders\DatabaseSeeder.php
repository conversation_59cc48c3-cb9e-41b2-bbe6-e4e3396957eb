<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     * Suit l'ordre chronologique des migrations
     */
    public function run(): void
    {
        $this->call([
                // 1. Utilisateurs et entreprises
            UserSeeder::class,
            CompanySeeder::class,

                // 2. Données de base (référentiels) - dépendent des entreprises
            CurrencySeeder::class,
            LocalizationSeeder::class,

                // 3. Profils d'entreprise (avec adresses polymorphiques)
            CompanyProfileSeeder::class,

                // 4. Départements
            DepartmentSeeder::class,

                // 5. Ajustements puis produits et services (avec relations polymorphiques)
            AdjustmentSeeder::class,
            OfferingSeeder::class,

                // 6. Stock et mouvements (gérés par les factories si nécessaire)

                // 7. Paramètres de documents
            DocumentDefaultSeeder::class,

                // 8. Clients
            ClientSeeder::class,

                // 9. Documents commerciaux (avec lignes polymorphiques)
            InvoiceSeeder::class,
            EstimateSeeder::class,

                // 10. Transactions (polymorphiques)
            TransactionSeeder::class,
        ]);
    }
}
