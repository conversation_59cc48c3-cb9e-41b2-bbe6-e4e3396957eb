<?php

namespace App\Filament\Company\Resources;


use App\Enums\AdjustmentCategory;
use App\Enums\AdjustmentType;
use App\Enums\OfferingType;
use App\Filament\Company\Resources\OfferingResource\Pages;
use App\Filament\Forms\Components\Banner;
use App\Filament\Forms\Components\AdjustmentSelect;
use App\Models\Offering;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use JaOcero\RadioDeck\Forms\Components\RadioDeck;
use App\Utilities\CurrencyAccessor;

class OfferingResource extends Resource
{
    protected static ?string $model = Offering::class;

    //protected static ?string $navigationIcon = 'heroicon-o-square-3-stack-3d';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Banner::make('inactiveAdjustments')
                    ->label('Inactive adjustments')
                    ->warning()
                    ->icon('heroicon-o-exclamation-triangle')
                    ->visible(fn(?Offering $record) => $record?->hasInactiveAdjustments())
                    ->columnSpanFull()
                    ->description(function (Offering $record) {
                        $inactiveAdjustments = collect();

                        foreach ($record->adjustments as $adjustment) {
                            if ($adjustment->isInactive() && $inactiveAdjustments->doesntContain($adjustment->name)) {
                                $inactiveAdjustments->push($adjustment->name);
                            }
                        }

                        $adjustmentsList = $inactiveAdjustments->map(static function ($name) {
                            return "<span class='font-medium'>{$name}</span>";
                        })->join(', ');

                        $output = "<p class='text-sm'>This offering contains inactive adjustments that need to be addressed: {$adjustmentsList}</p>";

                        return new HtmlString($output);
                    }),
                static::getGeneralSection(),
                // Sellable Section
                static::getSellableSection(),
                // Purchasable Section
                static::getPurchasableSection(),
            ])->columns();
    }

    public static function getGeneralSection(bool $hasAttributeChoices = true): Forms\Components\Section
    {
        return Forms\Components\Section::make('General')
            ->schema([
                RadioDeck::make('type')
                    ->options(OfferingType::class)
                    ->default(OfferingType::Product)
                    ->icons(OfferingType::class)
                    ->color('primary')
                    ->columns()
                    ->required(),
                Forms\Components\TextInput::make('name')
                    ->autofocus()
                    ->required()
                    ->columnStart(1)
                    ->maxLength(255),
                Forms\Components\TextInput::make('price')
                    ->required()
                    ->numeric()
                    ->step(0.01)
                    ->prefix(fn() => currency(CurrencyAccessor::getDefaultCurrency())->getSymbol()),
                Forms\Components\Textarea::make('description')
                    ->label('Description')
                    ->columnSpan(2)
                    ->rows(3),
                Forms\Components\CheckboxList::make('attributes')
                    ->options([
                        'Sellable' => 'Sellable',
                        'Purchasable' => 'Purchasable',
                    ])
                    ->visible($hasAttributeChoices)
                    ->hiddenLabel()
                    ->required()
                    ->live()
                    ->bulkToggleable()
                    ->afterStateHydrated(function (Forms\Components\CheckboxList $component, ?Offering $record) {
                        if (!$record) {
                            return;
                        }

                        $attributes = [];
                        if ($record->sellable) {
                            $attributes[] = 'Sellable';
                        }
                        if ($record->purchasable) {
                            $attributes[] = 'Purchasable';
                        }

                        $component->state($attributes);
                    })
                    ->dehydrateStateUsing(function ($state) {
                        // Cette fonction sera appelée lors de la sauvegarde
                        // pour convertir les attributs en champs sellable/purchasable
                        return $state;
                    })
                    ->validationMessages([
                        'required' => 'The offering must be either sellable or purchasable.',
                    ]),
            ])->columns();
    }

    public static function getSellableSection(): Forms\Components\Section
    {
        return Forms\Components\Section::make('Sale Information')
            ->schema([
                AdjustmentSelect::make('salesTaxes')
                    ->label('Sales tax')
                    ->category(AdjustmentCategory::Tax)
                    ->type(AdjustmentType::Sales)
                    ->multiple(),
                AdjustmentSelect::make('salesDiscounts')
                    ->label('Sales discount')
                    ->category(AdjustmentCategory::Discount)
                    ->type(AdjustmentType::Sales)
                    ->multiple(),
            ])
            ->columns()
            ->visible(static fn(Forms\Get $get) => in_array('Sellable', $get('attributes') ?? []));
    }

    public static function getPurchasableSection(): Forms\Components\Section
    {
        return Forms\Components\Section::make('Purchase Information')
            ->schema([
                AdjustmentSelect::make('purchaseTaxes')
                    ->label('Purchase tax')
                    ->category(AdjustmentCategory::Tax)
                    ->type(AdjustmentType::Purchase)
                    ->multiple(),
                AdjustmentSelect::make('purchaseDiscounts')
                    ->label('Purchase discount')
                    ->category(AdjustmentCategory::Discount)
                    ->type(AdjustmentType::Purchase)
                    ->multiple(),
            ])
            ->columns()
            ->visible(static fn(Forms\Get $get) => in_array('Purchasable', $get('attributes') ?? []));
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query) {
                $query->selectRaw("
                        *,
                        CONCAT_WS(' & ',
                            CASE WHEN sellable THEN 'Sellable' END,
                            CASE WHEN purchasable THEN 'Purchasable' END
                        ) AS attributes
                    ");
            })
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Name'),
                Tables\Columns\TextColumn::make('attributes')
                    ->label('Attributes')
                    ->badge(),
                Tables\Columns\TextColumn::make('type')
                    ->searchable(),
                Tables\Columns\TextColumn::make('price')
                    ->currency(CurrencyAccessor::getDefaultCurrency(), true)
                    ->sortable()
                    ->description(function (Offering $record) {
                        $adjustments = $record->adjustments()
                            ->pluck('name')
                            ->join(', ');

                        if (empty($adjustments)) {
                            return null;
                        }

                        $adjustmentsList = Str::of($adjustments)->limit(40);

                        return "+ {$adjustmentsList}";
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label('Type')
                    ->options(OfferingType::class)
                    ->multiple(),

                Tables\Filters\SelectFilter::make('sellable')
                    ->label('Sellable')
                    ->options([
                        1 => 'Yes',
                        0 => 'No',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (isset($data['value'])) {
                            return $query->where('sellable', (bool) $data['value']);
                        }
                        return $query;
                    }),

                Tables\Filters\SelectFilter::make('purchasable')
                    ->label('Purchasable')
                    ->options([
                        1 => 'Yes',
                        0 => 'No',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (isset($data['value'])) {
                            return $query->where('purchasable', (bool) $data['value']);
                        }
                        return $query;
                    }),

                Tables\Filters\Filter::make('price_range')
                    ->form([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('price_from')
                                    ->label('Price from')
                                    ->numeric()
                                    ->step(0.01),
                                Forms\Components\TextInput::make('price_to')
                                    ->label('Price to')
                                    ->numeric()
                                    ->step(0.01),
                            ]),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['price_from'],
                                fn(Builder $query, $price): Builder => $query->where('price', '>=', $price),
                            )
                            ->when(
                                $data['price_to'],
                                fn(Builder $query, $price): Builder => $query->where('price', '<=', $price),
                            );
                    }),

                Tables\Filters\SelectFilter::make('adjustments')
                    ->label('Has Adjustments')
                    ->options([
                        'with' => 'With adjustments',
                        'without' => 'Without adjustments',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if ($data['value'] === 'with') {
                            return $query->whereHas('adjustments');
                        } elseif ($data['value'] === 'without') {
                            return $query->whereDoesntHave('adjustments');
                        }
                        return $query;
                    }),

                Tables\Filters\SelectFilter::make('tax_adjustments')
                    ->label('Has Tax')
                    ->options([
                        'with' => 'With tax',
                        'without' => 'Without tax',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if ($data['value'] === 'with') {
                            return $query->whereHas('adjustments', function (Builder $query) {
                                $query->where('category', AdjustmentCategory::Tax);
                            });
                        } elseif ($data['value'] === 'without') {
                            return $query->whereDoesntHave('adjustments', function (Builder $query) {
                                $query->where('category', AdjustmentCategory::Tax);
                            });
                        }
                        return $query;
                    }),
            ], layout: Tables\Enums\FiltersLayout::Modal)
            ->filtersFormColumns(2)
            ->filtersFormWidth('2xl')
            ->filtersTriggerAction(
                fn(Tables\Actions\Action $action) => $action
                    ->slideOver()
                    ->button()
                    ->label('Filters')
                    ->icon('heroicon-o-funnel')
            )
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOfferings::route('/'),
            'create' => Pages\CreateOffering::route('/create'),
            'edit' => Pages\EditOffering::route('/{record}/edit'),
        ];
    }
}
