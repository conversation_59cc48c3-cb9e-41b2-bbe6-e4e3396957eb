<?php

namespace App\DTO;

use App\Enums\DocumentType;
use App\Enums\Font;
use App\Models\DocumentDefault;
use App\Utilities\CurrencyAccessor;

readonly class DocumentPreviewDTO extends DocumentDTO
{
    public static function fromSettings(DocumentDefault $settings, ?array $data = null): self
    {
        $company = $settings->company;

        $amountDue = $settings->type !== DocumentType::Estimate ?
            self::formatToMoney(95000, null) :
            null;

        return new self(
            header: $data['header'] ?? $settings->header ?? 'Invoice',
            subheader: $data['subheader'] ?? $settings->subheader,
            footer: $data['footer'] ?? $settings->footer,
            terms: $data['terms'] ?? $settings->terms,
            logo: $settings->logo_url,
            number: self::generatePreviewNumber($settings, $data),
            referenceNumber: $settings->getNumberNext('ORD-'),
            date: $company->locale->date_format->getLabel(),
            dueDate: $company->locale->date_format->value,
            currencyCode: CurrencyAccessor::getDefaultCurrency(),
            subtotal: self::formatToMoney(100000, null), // $1000.00
            discount: self::formatToMoney(10000, null), // $100.00
            tax: self::formatToMoney(5000, null), // $50.00
            total: self::formatToMoney(95000, null), // $950.00
            amountDue: $amountDue, // $950.00 or null for estimates
            company: CompanyDTO::fromModel($company),
            client: ClientPreviewDTO::fake(),
            lineItems: LineItemDTO::fakeItems($settings->type === DocumentType::Delivery),
            label: self::generatePreviewDocumentLabels($settings),

            lineItemColumns: LineItemColumnsDTO::fromDocumentDefault($settings),
            accentColor: $data['accent_color'] ?? $settings->accent_color ?? '#000000',
            showLogo: $data['show_logo'] ?? $settings->show_logo ?? true,
            font: Font::tryFrom($data['font']) ?? $settings->font ?? Font::Inter,
            showPaymentDetails: $data['show_payment_details'] ?? $settings->show_payment_details ?? true,
            paymentDetails: PaymentDetailsDTO::fake(),
            showAmountInWords: $data['text_with_amount_in_words'] ?? $settings->text_with_amount_in_words ?? true,
            amountInWords: AmountInWordsDTO::fake(),
            showSignature: $data['show_signature'] ?? $settings->show_signature ?? true,
            signature: SignatureDTO::fake(),
            showQRCode: $data['show_qr_code'] ?? $settings->show_qr_code ?? true,
            qrCode: QRCodeDTO::fake(),
            showHeader: $data['show_header'] ?? $settings->show_header ?? true,
            showClientDetails: $data['show_client_details'] ?? $settings->show_client_details ?? true,
            showObjetDetails: $data['show_objet_details'] ?? $settings->show_objet_details ?? true,
            showCompanyDetails: $data['show_company_details'] ?? $settings->show_company_details ?? true,
            showLines: $data['show_lines'] ?? $settings->show_lines ?? true,
            showTotals: $data['show_totals'] ?? $settings->show_totals ?? true,
            showDiscount: $data['show_discount'] ?? $settings->show_discount ?? true,
            showTax: $data['show_tax'] ?? $settings->show_tax ?? true,
            showNotes: $data['show_notes'] ?? $settings->show_notes ?? true,
            showFooter: $data['show_footer'] ?? $settings->show_footer ?? true,
        );
    }

    protected static function generatePreviewNumber(DocumentDefault $settings, ?array $data): string
    {
        $prefix = $data['number_prefix'] ?? $settings->number_prefix ?? 'INV-';

        return $settings->getNumberNext($prefix);
    }

    protected static function generatePreviewDocumentLabels(DocumentDefault $settings): DocumentLabelDTO
    {
        return new DocumentLabelDTO(
            title: $settings->type->getLabel(),
            number: 'Number',
            referenceNumber: 'Reference',
            date: 'Date',
            dueDate: 'Due Date',
            amountDue: $settings->type !== DocumentType::Estimate ? 'Amount Due' : null,
        );
    }
}
