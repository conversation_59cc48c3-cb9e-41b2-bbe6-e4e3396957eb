<?php

namespace App\Filament\Company\Resources\DepartmentResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ChildrenRelationManager extends RelationManager
{
    protected static string $relationship = 'children';

    protected static ?string $title = 'Sub-departments';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->placeholder('Sub-department name')
                    ->maxLength(255),

                Forms\Components\Select::make('manager_id')
                    ->relationship(
                        name: 'manager',
                        titleAttribute: 'name',
                        modifyQueryUsing: static function (Builder $query) {
                            $company = auth()->user()->currentCompany;
                            if (!$company) {
                                return $query->whereRaw('1 = 0');
                            }
                            $companyUsers = $company->allUsers()->pluck('id')->toArray();
                            return $query->whereIn('id', $companyUsers);
                        }
                    )
                    ->placeholder('Select manager')
                    ->searchable()
                    ->preload()
                    ->nullable(),

                Forms\Components\Textarea::make('description')
                    ->placeholder('Sub-department description')
                    ->rows(3)
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Name')
                    ->weight('semibold')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('manager.name')
                    ->label('Manager')
                    ->searchable()
                    ->sortable()
                    ->placeholder('No manager assigned'),

                Tables\Columns\TextColumn::make('employeeships_count')
                    ->label('Employees')
                    ->badge()
                    ->counts('employeeships')
                    ->color('success'),

                Tables\Columns\TextColumn::make('children_count')
                    ->label('Sub-departments')
                    ->badge()
                    ->counts('children')
                    ->color('info'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('manager_id')
                    ->relationship('manager', 'name')
                    ->label('Manager')
                    ->placeholder('All managers'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('name', 'asc');
    }
}
