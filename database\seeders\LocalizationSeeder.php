<?php

namespace Database\Seeders;

use App\Models\Localization;
use App\Models\Company;
use App\Enums\DateFormat;
use App\Enums\TimeFormat;
use App\Enums\WeekStart;
use App\Enums\NumberFormat;
use App\Utilities\Timezone;
use Illuminate\Database\Seeder;
use Symfony\Component\Intl\Countries;
use Symfony\Component\Intl\Languages;
use Symfony\Component\Intl\Timezones;
use DateTimeZone;

class LocalizationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $companies = Company::all();

        if ($companies->isEmpty()) {
            $this->command->warn('Aucune entreprise trouvée. Exécutez CompanySeeder en premier.');
            return;
        }

        // Générer les localisations en utilisant le package Intl
        $localizations = $this->generateLocalizations();

        // Créer les localisations pour chaque entreprise
        foreach ($companies as $company) {
            // Une seule localisation par entreprise (la première de la liste)
            $localizationData = $localizations[0];

            $existing = Localization::where('company_id', $company->id)->first();

            if (!$existing) {
                Localization::create(array_merge($localizationData, [
                    'company_id' => $company->id,
                    'created_by' => $company->user_id,
                    'updated_by' => $company->user_id,
                ]));

                $this->command->info("Localisation créée pour l'entreprise: {$company->name}");
            } else {
                $this->command->info("Localisation existante pour l'entreprise: {$company->name}");
            }
        }

        $this->command->info('Localisations créées avec succès pour toutes les entreprises !');
        $this->command->info('Nombre total de localisations disponibles: ' . count($localizations));
    }

    /**
     * Génère une liste de localisations en utilisant le package Symfony Intl
     */
    private function generateLocalizations(): array
    {
        $localizations = [];

        // Définir les configurations par région/langue
        $regionConfigs = [
            // Europe
            'fr_FR' => [
                'language' => 'fr',
                'timezone' => 'Europe/Paris',
                'date_format' => DateFormat::DMY_SLASH,
                'time_format' => TimeFormat::H24,
                'week_start' => WeekStart::Monday,
                'number_format' => NumberFormat::SpaceComma,
                'percent_first' => false,
            ],
            'en_GB' => [
                'language' => 'en',
                'timezone' => 'Europe/London',
                'date_format' => DateFormat::DMY_SLASH,
                'time_format' => TimeFormat::H24,
                'week_start' => WeekStart::Monday,
                'number_format' => NumberFormat::CommaDot,
                'percent_first' => false,
            ],
            'de_DE' => [
                'language' => 'de',
                'timezone' => 'Europe/Berlin',
                'date_format' => DateFormat::DMY_DOT,
                'time_format' => TimeFormat::H24,
                'week_start' => WeekStart::Monday,
                'number_format' => NumberFormat::DotComma,
                'percent_first' => false,
            ],
            'es_ES' => [
                'language' => 'es',
                'timezone' => 'Europe/Madrid',
                'date_format' => DateFormat::DMY_SLASH,
                'time_format' => TimeFormat::H24,
                'week_start' => WeekStart::Monday,
                'number_format' => NumberFormat::DotComma,
                'percent_first' => false,
            ],
            'it_IT' => [
                'language' => 'it',
                'timezone' => 'Europe/Rome',
                'date_format' => DateFormat::DMY_SLASH,
                'time_format' => TimeFormat::H24,
                'week_start' => WeekStart::Monday,
                'number_format' => NumberFormat::DotComma,
                'percent_first' => false,
            ],
            // Amérique du Nord
            'en_US' => [
                'language' => 'en',
                'timezone' => 'America/New_York',
                'date_format' => DateFormat::MDY_SLASH,
                'time_format' => TimeFormat::H12_CAP,
                'week_start' => WeekStart::Sunday,
                'number_format' => NumberFormat::CommaDot,
                'percent_first' => false,
            ],
            'en_CA' => [
                'language' => 'en',
                'timezone' => 'America/Toronto',
                'date_format' => DateFormat::DMY_SLASH,
                'time_format' => TimeFormat::H12_CAP,
                'week_start' => WeekStart::Sunday,
                'number_format' => NumberFormat::CommaDot,
                'percent_first' => false,
            ],
            'fr_CA' => [
                'language' => 'fr',
                'timezone' => 'America/Montreal',
                'date_format' => DateFormat::YMD_DASH,
                'time_format' => TimeFormat::H24,
                'week_start' => WeekStart::Sunday,
                'number_format' => NumberFormat::SpaceComma,
                'percent_first' => false,
            ],
        ];

        foreach ($regionConfigs as $locale => $config) {
            $localizations[] = array_merge($config, [
                'fiscal_year_end_month' => 12,
                'fiscal_year_end_day' => 31,
            ]);
        }

        return $localizations;
    }

    /**
     * Affiche des informations sur les time zones disponibles en utilisant Intl
     */
    private function displayTimezoneInfo(): void
    {
        $this->command->info('=== Informations sur les Time Zones (Symfony Intl) ===');

        // Obtenir les noms des time zones localisés
        $timezoneNames = Timezones::getNames();

        // Afficher quelques exemples
        $exampleTimezones = [
            'Europe/Paris',
            'America/New_York',
            'Asia/Tokyo',
            'Africa/Abidjan',
            'Australia/Sydney'
        ];

        foreach ($exampleTimezones as $timezone) {
            if (isset($timezoneNames[$timezone])) {
                $this->command->line("- {$timezone}: {$timezoneNames[$timezone]}");
            }
        }

        $this->command->info('Total time zones disponibles: ' . count($timezoneNames));
    }

    /**
     * Obtient les time zones pour un pays spécifique
     */
    private function getTimezonesForCountry(string $countryCode): array
    {
        try {
            return \DateTimeZone::listIdentifiers(\DateTimeZone::PER_COUNTRY, strtoupper($countryCode));
        } catch (\Exception $e) {
            $this->command->warn("Impossible d'obtenir les time zones pour le pays: {$countryCode}");
            return [];
        }
    }

    /**
     * Génère des localisations basées sur les pays disponibles dans Intl
     */
    private function generateLocalizationsFromCountries(): array
    {
        $localizations = [];
        $countries = Countries::getNames();

        // Sélectionner quelques pays populaires
        $popularCountries = ['FR', 'US', 'GB', 'DE', 'CA', 'ES', 'IT', 'JP', 'AU'];

        foreach ($popularCountries as $countryCode) {
            if (isset($countries[$countryCode])) {
                $timezones = $this->getTimezonesForCountry($countryCode);

                if (!empty($timezones)) {
                    $config = $this->getDefaultConfigForCountry($countryCode);
                    $config['timezone'] = $timezones[0]; // Premier timezone du pays

                    $localizations[] = $config;
                }
            }
        }

        return $localizations;
    }

    /**
     * Retourne la configuration par défaut pour un pays
     */
    private function getDefaultConfigForCountry(string $countryCode): array
    {
        $defaults = [
            'fiscal_year_end_month' => 12,
            'fiscal_year_end_day' => 31,
        ];

        switch ($countryCode) {
            case 'FR':
                return array_merge($defaults, [
                    'language' => 'fr',
                    'date_format' => DateFormat::DMY_SLASH,
                    'time_format' => TimeFormat::H24,
                    'week_start' => WeekStart::Monday,
                    'number_format' => NumberFormat::SpaceComma,
                    'percent_first' => false,
                ]);

            case 'US':
                return array_merge($defaults, [
                    'language' => 'en',
                    'date_format' => DateFormat::MDY_SLASH,
                    'time_format' => TimeFormat::H12_CAP,
                    'week_start' => WeekStart::Sunday,
                    'number_format' => NumberFormat::CommaDot,
                    'percent_first' => false,
                ]);

            case 'GB':
                return array_merge($defaults, [
                    'language' => 'en',
                    'date_format' => DateFormat::DMY_SLASH,
                    'time_format' => TimeFormat::H24,
                    'week_start' => WeekStart::Monday,
                    'number_format' => NumberFormat::CommaDot,
                    'percent_first' => false,
                ]);

            case 'DE':
                return array_merge($defaults, [
                    'language' => 'de',
                    'date_format' => DateFormat::DMY_DOT,
                    'time_format' => TimeFormat::H24,
                    'week_start' => WeekStart::Monday,
                    'number_format' => NumberFormat::DotComma,
                    'percent_first' => false,
                ]);

            default:
                return array_merge($defaults, [
                    'language' => 'en',
                    'date_format' => DateFormat::DMY_SLASH,
                    'time_format' => TimeFormat::H24,
                    'week_start' => WeekStart::Monday,
                    'number_format' => NumberFormat::CommaDot,
                    'percent_first' => false,
                ]);
        }
    }
}
