<?php

namespace App\Filament\Company\Resources\DocumentDefaultResource\Pages;

use Filament\Actions;
use App\Traits\HandlePageRedirect;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Contracts\Support\Htmlable;
use App\Filament\Company\Resources\DocumentDefaultResource;

class EditDocumentDefault extends EditRecord
{
    use HandlePageRedirect;

    protected static string $resource = DocumentDefaultResource::class;

    public function getRecordTitle(): string|Htmlable
    {
        return $this->record->type->getLabel();
    }
}
