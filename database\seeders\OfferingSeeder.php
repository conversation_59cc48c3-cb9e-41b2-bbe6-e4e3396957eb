<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\Offering;
use App\Models\Adjustment;
use App\Enums\AdjustmentType;
use App\Enums\AdjustmentCategory;
use Illuminate\Database\Seeder;

class OfferingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $companies = Company::all();

        if ($companies->isEmpty()) {
            $this->command->warn('Aucune entreprise trouvée. Exécutez CompanySeeder en premier.');
            return;
        }

        foreach ($companies->take(3) as $company) {
            $this->createOfferingsForCompany($company);
        }

        // Produits/services supplémentaires pour les autres entreprises
        $remainingCompanies = $companies->skip(3);
        foreach ($remainingCompanies as $company) {
            $this->createOfferingsForCompany($company);
        }

        $this->command->info('Produits et services créés avec succès !');
    }

    private function createOfferingsForCompany(Company $company): void
    {
        $offerings = [
            [
                'name' => 'Consultation IT',
                'description' => 'Service de consultation en informatique',
                'price' => 75.00,
                'sellable' => true,
                'purchasable' => false,
            ],
            [
                'name' => 'Ordinateur Portable',
                'description' => 'Ordinateur portable professionnel',
                'price' => 899.99,
                'sellable' => true,
                'purchasable' => true,
            ],
            [
                'name' => 'Formation Web',
                'description' => 'Formation développement web',
                'price' => 1200.00,
                'sellable' => true,
                'purchasable' => false,
            ],
            [
                'name' => 'Licence Logiciel',
                'description' => 'Licence annuelle logiciel professionnel',
                'price' => 299.99,
                'sellable' => true,
                'purchasable' => true,
            ],
            [
                'name' => 'Maintenance Serveur',
                'description' => 'Service de maintenance serveur mensuel',
                'price' => 150.00,
                'sellable' => true,
                'purchasable' => false,
            ],
        ];

        foreach ($offerings as $offeringData) {
            $offering = Offering::create(array_merge($offeringData, [
                'company_id' => $company->id,
                'created_by' => $company->user_id,
                'updated_by' => $company->user_id,
            ]));

            // Créer les relations polymorphiques avec les ajustements
            $this->attachAdjustmentsToOffering($offering);
        }
    }

    /**
     * Attacher des ajustements polymorphiques à un produit/service
     */
    private function attachAdjustmentsToOffering(Offering $offering): void
    {
        $adjustments = Adjustment::where('company_id', $offering->company_id)->get();

        if ($adjustments->isEmpty()) {
            return;
        }

        // Attacher des taxes de vente si le produit est vendable
        if ($offering->sellable) {
            $salesTaxes = $adjustments->where('type', AdjustmentType::Sales)
                ->where('category', AdjustmentCategory::Tax)
                ->take(1);

            foreach ($salesTaxes as $tax) {
                $offering->adjustments()->attach($tax->id);
            }

            // Attacher des remises de vente (optionnel, 50% de chance)
            if (fake()->boolean(50)) {
                $salesDiscounts = $adjustments->where('type', AdjustmentType::Sales)
                    ->where('category', AdjustmentCategory::Discount)
                    ->take(1);

                foreach ($salesDiscounts as $discount) {
                    $offering->adjustments()->attach($discount->id);
                }
            }
        }

        // Attacher des taxes d'achat si le produit est achetable
        if ($offering->purchasable) {
            $purchaseTaxes = $adjustments->where('type', AdjustmentType::Purchase)
                ->where('category', AdjustmentCategory::Tax)
                ->take(1);

            foreach ($purchaseTaxes as $tax) {
                $offering->adjustments()->attach($tax->id);
            }
        }
    }
}
