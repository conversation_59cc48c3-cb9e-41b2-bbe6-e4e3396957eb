<?php

namespace App\DTO;

use Carbon\Carbon;

readonly class QRCodeDTO
{
    public function __construct(
        public ?string $qrCodeImage,
        public string $invoiceCode,
        public string $documentNumber,
        public string $creationTime,
    ) {}

    /**
     * Propriété calculée pour l'URL du QR code (alias pour qrCodeImage)
     */
    public function getUrlAttribute(): ?string
    {
        return $this->qrCodeImage;
    }

    /**
     * Getter magique pour la propriété url
     */
    public function __get(string $name): mixed
    {
        if ($name === 'url') {
            return $this->qrCodeImage;
        }

        throw new \InvalidArgumentException("Property {$name} does not exist on " . static::class);
    }

    public static function fromDocument($document, ?string $qrCodeData = null): self
    {
        // Générer le code facture au format XXXX-XXXX-XXXX-XXXX-XXXX
        $invoiceCode = self::generateInvoiceCode($document->documentNumber());

        // Récupérer le numéro de base du document
        $documentNumber = $document->documentNumber() ?? 'N/A';

        // Formater l'heure de création
        $creationTime = $document->created_at
            ? $document->created_at->format('d/m/Y H:i:s')
            : Carbon::now()->format('d/m/Y H:i:s');

        return new self(
            qrCodeImage: $qrCodeData,
            invoiceCode: $invoiceCode,
            documentNumber: $documentNumber,
            creationTime: $creationTime,
        );
    }

    public static function fake(): self
    {
        return new self(
            qrCodeImage: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
            invoiceCode: 'INV-2024-0001-ABCD-EFGH',
            documentNumber: 'INV-001',
            creationTime: '23/07/2025 14:30:25',
        );
    }

    private static function generateInvoiceCode(string $baseNumber): string
    {
        // Nettoyer le numéro de base
        $cleanNumber = preg_replace('/[^A-Za-z0-9]/', '', $baseNumber);

        // Générer des segments aléatoires pour compléter
        $segments = [
            $cleanNumber,
            strtoupper(substr(md5($cleanNumber . time()), 0, 4)),
            strtoupper(substr(md5($cleanNumber . 'salt1'), 0, 4)),
            strtoupper(substr(md5($cleanNumber . 'salt2'), 0, 4)),
            strtoupper(substr(md5($cleanNumber . 'salt3'), 0, 4)),
        ];

        return implode('-', $segments);
    }

    public function hasQRCode(): bool
    {
        return !empty($this->qrCodeImage) || !empty($this->invoiceCode) || !empty($this->documentNumber);
    }

    public function toArray(): array
    {
        return [
            'qr_code_image' => $this->qrCodeImage,
            'invoice_code' => $this->invoiceCode,
            'document_number' => $this->documentNumber,
            'creation_time' => $this->creationTime,
        ];
    }
}
