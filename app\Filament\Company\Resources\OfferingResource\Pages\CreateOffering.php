<?php

namespace App\Filament\Company\Resources\OfferingResource\Pages;

use Filament\Actions;
use App\Traits\HandlePageRedirect;
use Filament\Resources\Pages\CreateRecord;
use App\Filament\Company\Resources\OfferingResource;

class CreateOffering extends CreateRecord
{
    use HandlePageRedirect;

    protected static string $resource = OfferingResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        if (isset($data['attributes'])) {
            $attributes = $data['attributes'] ?? [];
            $data['sellable'] = in_array('Sellable', $attributes);
            $data['purchasable'] = in_array('Purchasable', $attributes);
            unset($data['attributes']);
        }
        return $data;
    }
}
