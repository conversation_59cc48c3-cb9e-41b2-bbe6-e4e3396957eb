<?php

namespace App\Filament\Company\Resources;


use App\Enums\DocumentDiscountMethod;
use App\Enums\DocumentType;
use App\Enums\Font;
use App\Enums\Template;
use App\Filament\Company\Clusters\Settings;
use App\Filament\Company\Resources\DocumentDefaultResource\Pages;
use App\Filament\Forms\Components\DocumentPreview;
use App\Models\DocumentDefault;
use Filament\Forms;
use Filament\Forms\Components\Component;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class DocumentDefaultResource extends Resource
{
    protected static ?string $model = DocumentDefault::class;

    protected static ?string $cluster = Settings::class;

    //protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $modelLabel = 'document template';

    public static function form(Form $form): Form
    {
        return $form
            ->live()
            ->schema([
                self::getGeneralSection(),
                self::getContentSection(),
                self::getDisplayOptionsSection(),
                self::getTemplateSection(),
                self::getBillColumnLabelsSection(),
                self::getLabelsAndSignatureSection(),
            ]);
    }

    public static function getGeneralSection(): Forms\Components\Component
    {
        return Forms\Components\Section::make('General')
            ->schema([
                Forms\Components\TextInput::make('number_prefix')
                    ->label('Number Prefix')
                    ->placeholder('INV-, EST-, etc.')
                    ->nullable(),
                Forms\Components\Select::make('discount_method')
                    ->label('Discount Method')
                    ->required()
                    ->options(DocumentDiscountMethod::class),
                Forms\Components\TextInput::make('currency_code')
                    ->label('Currency Code')
                    ->placeholder('USD, EUR, XOF, etc.')
                    ->maxLength(3)
                    ->nullable(),
                Forms\Components\Textarea::make('notes')
                    ->label('Default Notes')
                    ->placeholder('Additional notes for documents...')
                    ->nullable(),
            ])->columns(2);
    }

    public static function getContentSection(): Forms\Components\Component
    {
        return Forms\Components\Section::make('Content')
            ->hidden(static fn(?DocumentDefault $record) => $record?->type === DocumentType::Bill)
            ->schema([
                Forms\Components\TextInput::make('header')
                    ->label('Header')
                    ->placeholder('Invoice, Estimate, etc.')
                    ->nullable(),
                Forms\Components\TextInput::make('subheader')
                    ->label('SubHeader')
                    ->placeholder('Additional subtitle text')
                    ->nullable(),
                Forms\Components\Textarea::make('footer')
                    ->label('Footer')
                    ->placeholder('Thank you for your business...')
                    ->nullable(),
                Forms\Components\Textarea::make('terms')
                    ->label('Terms & Conditions')
                    ->placeholder('Terms and conditions text...')
                    ->nullable(),
                Forms\Components\Checkbox::make('text_with_amount_in_words')
                    ->label('Show Amount in Words')
                    ->default(false),
            ])->columns(2);
    }

    public static function getDisplayOptionsSection(): Forms\Components\Component
    {
        return Forms\Components\Section::make('Display Options')
            ->description('Configure which sections to show or hide in the document')
            ->schema([
                Forms\Components\Grid::make(3)
                    ->schema([
                        Forms\Components\Checkbox::make('show_header')
                            ->label('Show Header')
                            ->default(true),
                        Forms\Components\Checkbox::make('show_client_details')
                            ->label('Show Client Details')
                            ->default(true),
                        Forms\Components\Checkbox::make('show_company_details')
                            ->label('Show Company Details')
                            ->default(true),
                        Forms\Components\Checkbox::make('show_lines')
                            ->label('Show Line Items')
                            ->default(true),
                        Forms\Components\Checkbox::make('show_totals')
                            ->label('Show Totals')
                            ->default(true),
                        Forms\Components\Checkbox::make('show_discount')
                            ->label('Show Discount')
                            ->default(true),
                        Forms\Components\Checkbox::make('show_tax')
                            ->label('Show Tax')
                            ->default(true),
                        Forms\Components\Checkbox::make('show_payment_details')
                            ->label('Show Payment Details')
                            ->default(true),
                        Forms\Components\Checkbox::make('show_notes')
                            ->label('Show Notes')
                            ->default(true),
                        Forms\Components\Checkbox::make('show_signature')
                            ->label('Show Signature')
                            ->default(false),
                        Forms\Components\Checkbox::make('show_footer')
                            ->label('Show Footer')
                            ->default(true),
                        Forms\Components\Checkbox::make('show_qr_code')
                            ->label('Show QR Code')
                            ->default(false),
                    ]),
            ]);
    }

    public static function getTemplateSection(): Component
    {
        return Forms\Components\Section::make('Template')
            ->description('Choose the template and edit the column names.')
            ->hidden(static fn(?DocumentDefault $record) => $record?->type === DocumentType::Bill)
            ->schema([
                Forms\Components\Grid::make(1)
                    ->schema([
                        Forms\Components\FileUpload::make('logo')
                            ->maxSize(1024)
                            ->label('Logo')
                            ->openable()
                            ->directory('logos/document')
                            ->image()
                            ->imageCropAspectRatio('3:2')
                            ->panelAspectRatio('3:2')
                            ->panelLayout('compact')
                            ->extraAttributes([
                                'class' => 'es-file-upload document-logo-preview',
                            ])
                            ->loadingIndicatorPosition('left')
                            ->removeUploadedFileButtonPosition('right'),
                        Forms\Components\Checkbox::make('show_logo')
                            ->label(''),
                        Forms\Components\ColorPicker::make('accent_color')
                            ->label(''),
                        Forms\Components\Select::make('font')
                            ->required()
                            ->label('Font')
                            ->allowHtml()
                            ->options(
                                collect(Font::cases())
                                    ->mapWithKeys(static fn($case) => [
                                        $case->value => "<span style='font-family:{$case->getLabel()}'>{$case->getLabel()}</span>",
                                    ]),
                            ),
                        Forms\Components\Select::make('template')
                            ->required()
                            ->label('')
                            ->options(Template::class),
                        ...static::getColumnLabelsSchema(),
                    ])->columnSpan(1),
                DocumentPreview::make()
                    ->template(static fn(Get $get) => Template::parse($get('template')) ?? Template::Default)
                    ->preview()
                    ->columnSpan([
                        'lg' => 2,
                    ]),
            ])->columns(3);
    }

    public static function getBillColumnLabelsSection(): Component
    {
        return Forms\Components\Section::make('Column Labels')
            ->visible(static fn(?DocumentDefault $record) => $record?->type === DocumentType::Bill)
            ->schema(static::getColumnLabelsSchema())->columns();
    }

    public static function getLabelsAndSignatureSection(): Component
    {
        return Forms\Components\Section::make('Labels & Signature Configuration')
            ->description('Customize labels and signature settings')
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        // Colonne 1: Client & Document Labels
                        Forms\Components\Section::make('Client & Document Labels')
                            ->schema([
                                Forms\Components\Select::make('client_name.option')
                                    ->label('Client Name Label')
                                    ->options(DocumentDefault::getAvailableClientNameOptions())
                                    ->afterStateUpdated(static function (Get $get, Set $set, $state, $old) {
                                        if ($state !== 'other' && $old === 'other' && filled($get('client_name.custom'))) {
                                            $set('client_name.old_custom', $get('client_name.custom'));
                                            $set('client_name.custom', null);
                                        }
                                        if ($state === 'other' && $old !== 'other') {
                                            $set('client_name.custom', $get('client_name.old_custom'));
                                        }
                                    }),
                                Forms\Components\TextInput::make('client_name.custom')
                                    ->hiddenLabel()
                                    ->disabled(static fn(callable $get) => $get('client_name.option') !== 'other')
                                    ->nullable(),

                                Forms\Components\Select::make('client_register.option')
                                    ->label('Client Tax ID Label')
                                    ->options(DocumentDefault::getAvailableClientTaxIDNameOptions())
                                    ->afterStateUpdated(static function (Get $get, Set $set, $state, $old) {
                                        if ($state !== 'other' && $old === 'other' && filled($get('client_register.custom'))) {
                                            $set('client_register.old_custom', $get('client_register.custom'));
                                            $set('client_register.custom', null);
                                        }
                                        if ($state === 'other' && $old !== 'other') {
                                            $set('client_register.custom', $get('client_register.old_custom'));
                                        }
                                    }),
                                Forms\Components\TextInput::make('client_register.custom')
                                    ->hiddenLabel()
                                    ->disabled(static fn(callable $get) => $get('client_register.option') !== 'other')
                                    ->nullable(),

                                Forms\Components\Select::make('client_address.option')
                                    ->label('Client Address Label')
                                    ->options(DocumentDefault::getAvailableClientAddressNameOptions())
                                    ->afterStateUpdated(static function (Get $get, Set $set, $state, $old) {
                                        if ($state !== 'other' && $old === 'other' && filled($get('client_address.custom'))) {
                                            $set('client_address.old_custom', $get('client_address.custom'));
                                            $set('client_address.custom', null);
                                        }
                                        if ($state === 'other' && $old !== 'other') {
                                            $set('client_address.custom', $get('client_address.old_custom'));
                                        }
                                    }),
                                Forms\Components\TextInput::make('client_address.custom')
                                    ->hiddenLabel()
                                    ->disabled(static fn(callable $get) => $get('client_address.option') !== 'other')
                                    ->nullable(),

                                Forms\Components\Select::make('object_name.option')
                                    ->label('Document Object Label')
                                    ->options(DocumentDefault::getAvailableDocumentObjectNameOptions())
                                    ->afterStateUpdated(static function (Get $get, Set $set, $state, $old) {
                                        if ($state !== 'other' && $old === 'other' && filled($get('object_name.custom'))) {
                                            $set('object_name.old_custom', $get('object_name.custom'));
                                            $set('object_name.custom', null);
                                        }
                                        if ($state === 'other' && $old !== 'other') {
                                            $set('object_name.custom', $get('object_name.old_custom'));
                                        }
                                    }),
                                Forms\Components\TextInput::make('object_name.custom')
                                    ->hiddenLabel()
                                    ->disabled(static fn(callable $get) => $get('object_name.option') !== 'other')
                                    ->nullable(),

                                Forms\Components\Select::make('object_key_value.option')
                                    ->label('Document Object Value')
                                    ->options(DocumentDefault::getAvailableDocumentObjectValueNameOptions())
                                    ->afterStateUpdated(static function (Get $get, Set $set, $state, $old) {
                                        if ($state !== 'other' && $old === 'other' && filled($get('object_key_value.custom'))) {
                                            $set('object_key_value.old_custom', $get('object_key_value.custom'));
                                            $set('object_key_value.custom', null);
                                        }
                                        if ($state === 'other' && $old !== 'other') {
                                            $set('object_key_value.custom', $get('object_key_value.old_custom'));
                                        }
                                    }),
                                Forms\Components\TextInput::make('object_key_value.custom')
                                    ->hiddenLabel()
                                    ->disabled(static fn(callable $get) => $get('object_key_value.option') !== 'other')
                                    ->nullable(),
                            ])->columnspan(1),

                        // Colonne 2: Signature & Authorization
                        Forms\Components\Section::make('Signature & Authorization')
                            ->schema([
                                Forms\Components\FileUpload::make('authorized_personnel_signature')
                                    ->label('Signature Image')
                                    ->image()
                                    ->directory('signatures')
                                    ->maxSize(1024)
                                    ->nullable(),

                                Forms\Components\Select::make('authorized_personnel_signature_name.option')
                                    ->label('Authorized Personnel Name')
                                    ->options(DocumentDefault::getAvailableAuthorizedPersonnelSignatureNameOptions())
                                    ->afterStateUpdated(static function (Get $get, Set $set, $state, $old) {
                                        if ($state !== 'other' && $old === 'other' && filled($get('authorized_personnel_signature_name.custom'))) {
                                            $set('authorized_personnel_signature_name.old_custom', $get('authorized_personnel_signature_name.custom'));
                                            $set('authorized_personnel_signature_name.custom', null);
                                        }
                                        if ($state === 'other' && $old !== 'other') {
                                            $set('authorized_personnel_signature_name.custom', $get('authorized_personnel_signature_name.old_custom'));
                                        }
                                    }),
                                Forms\Components\TextInput::make('authorized_personnel_signature_name.custom')
                                    ->hiddenLabel()
                                    ->disabled(static fn(callable $get) => $get('authorized_personnel_signature_name.option') !== 'other')
                                    ->nullable(),

                                Forms\Components\Select::make('authorized_personnel_signature_role.option')
                                    ->label('Authorized Personnel Role')
                                    ->options(DocumentDefault::getAvailableAuthorizedPersonnelSignatureRoleNameOptions())
                                    ->afterStateUpdated(static function (Get $get, Set $set, $state, $old) {
                                        if ($state !== 'other' && $old === 'other' && filled($get('authorized_personnel_signature_role.custom'))) {
                                            $set('authorized_personnel_signature_role.old_custom', $get('authorized_personnel_signature_role.custom'));
                                            $set('authorized_personnel_signature_role.custom', null);
                                        }
                                        if ($state === 'other' && $old !== 'other') {
                                            $set('authorized_personnel_signature_role.custom', $get('authorized_personnel_signature_role.old_custom'));
                                        }
                                    }),
                                Forms\Components\TextInput::make('authorized_personnel_signature_role.custom')
                                    ->hiddenLabel()
                                    ->disabled(static fn(callable $get) => $get('authorized_personnel_signature_role.option') !== 'other')
                                    ->nullable(),

                                Forms\Components\Select::make('payment_type_name.option')
                                    ->label('Payment Type Label')
                                    ->options(DocumentDefault::getAvailablePaymentTypeNameOptions())
                                    ->afterStateUpdated(static function (Get $get, Set $set, $state, $old) {
                                        if ($state !== 'other' && $old === 'other' && filled($get('payment_type_name.custom'))) {
                                            $set('payment_type_name.old_custom', $get('payment_type_name.custom'));
                                            $set('payment_type_name.custom', null);
                                        }
                                        if ($state === 'other' && $old !== 'other') {
                                            $set('payment_type_name.custom', $get('payment_type_name.old_custom'));
                                        }
                                    }),
                                Forms\Components\TextInput::make('payment_type_name.custom')
                                    ->hiddenLabel()
                                    ->disabled(static fn(callable $get) => $get('payment_type_name.option') !== 'other')
                                    ->nullable(),
                            ])->columnspan(1),
                    ]),
            ])->columns(3);
    }

    public static function getColumnLabelsSchema(): array
    {
        return [
            // Champ Item Name - toujours affiché
            Forms\Components\Select::make('item_name.option')
                ->required()
                ->label('Item name')
                ->options(DocumentDefault::getAvailableItemNameOptions())
                ->afterStateUpdated(static function (Get $get, Set $set, $state, $old) {
                    if ($state !== 'other' && $old === 'other' && filled($get('item_name.custom'))) {
                        $set('item_name.old_custom', $get('item_name.custom'));
                        $set('item_name.custom', null);
                    }

                    if ($state === 'other' && $old !== 'other') {
                        $set('item_name.custom', $get('item_name.old_custom'));
                    }
                }),
            Forms\Components\TextInput::make('item_name.custom')
                ->hiddenLabel()
                ->extraFieldWrapperAttributes(static fn(?DocumentDefault $record) => [
                    'class' => $record?->type === DocumentType::Bill ? 'report-hidden-label' : '',
                ])
                ->disabled(static fn(callable $get) => $get('item_name.option') !== 'other')
                ->nullable(),

            // Champs pour documents NON-delivery (Invoice, Estimate, Bill)
            Forms\Components\Select::make('unit_name.option')
                ->required()
                ->label('Unit name')
                ->options(DocumentDefault::getAvailableUnitNameOptions())
                ->visible(static fn(?DocumentDefault $record) => $record?->type !== DocumentType::Delivery)
                ->afterStateUpdated(static function (Get $get, Set $set, $state, $old) {
                    if ($state !== 'other' && $old === 'other' && filled($get('unit_name.custom'))) {
                        $set('unit_name.old_custom', $get('unit_name.custom'));
                        $set('unit_name.custom', null);
                    }

                    if ($state === 'other' && $old !== 'other') {
                        $set('unit_name.custom', $get('unit_name.old_custom'));
                    }
                }),
            Forms\Components\TextInput::make('unit_name.custom')
                ->hiddenLabel()
                ->extraFieldWrapperAttributes(static fn(?DocumentDefault $record) => [
                    'class' => $record?->type === DocumentType::Bill ? 'report-hidden-label' : '',
                ])
                ->visible(static fn(?DocumentDefault $record) => $record?->type !== DocumentType::Delivery)
                ->disabled(static fn(callable $get) => $get('unit_name.option') !== 'other')
                ->nullable(),

            Forms\Components\Select::make('price_name.option')
                ->required()
                ->label('Price name')
                ->options(DocumentDefault::getAvailablePriceNameOptions())
                ->visible(static fn(?DocumentDefault $record) => $record?->type !== DocumentType::Delivery)
                ->afterStateUpdated(static function (Get $get, Set $set, $state, $old) {
                    if ($state !== 'other' && $old === 'other' && filled($get('price_name.custom'))) {
                        $set('price_name.old_custom', $get('price_name.custom'));
                        $set('price_name.custom', null);
                    }

                    if ($state === 'other' && $old !== 'other') {
                        $set('price_name.custom', $get('price_name.old_custom'));
                    }
                }),
            Forms\Components\TextInput::make('price_name.custom')
                ->hiddenLabel()
                ->extraFieldWrapperAttributes(static fn(?DocumentDefault $record) => [
                    'class' => $record?->type === DocumentType::Bill ? 'report-hidden-label' : '',
                ])
                ->visible(static fn(?DocumentDefault $record) => $record?->type !== DocumentType::Delivery)
                ->disabled(static fn(callable $get) => $get('price_name.option') !== 'other')
                ->nullable(),

            Forms\Components\Select::make('amount_name.option')
                ->required()
                ->label('Amount name')
                ->options(DocumentDefault::getAvailableAmountNameOptions())
                ->visible(static fn(?DocumentDefault $record) => $record?->type !== DocumentType::Delivery)
                ->afterStateUpdated(static function (Get $get, Set $set, $state, $old) {
                    if ($state !== 'other' && $old === 'other' && filled($get('amount_name.custom'))) {
                        $set('amount_name.old_custom', $get('amount_name.custom'));
                        $set('amount_name.custom', null);
                    }

                    if ($state === 'other' && $old !== 'other') {
                        $set('amount_name.custom', $get('amount_name.old_custom'));
                    }
                }),
            Forms\Components\TextInput::make('amount_name.custom')
                ->hiddenLabel()
                ->extraFieldWrapperAttributes(static fn(?DocumentDefault $record) => [
                    'class' => $record?->type === DocumentType::Bill ? 'report-hidden-label' : '',
                ])
                ->visible(static fn(?DocumentDefault $record) => $record?->type !== DocumentType::Delivery)
                ->disabled(static fn(callable $get) => $get('amount_name.option') !== 'other')
                ->nullable(),

            Forms\Components\Select::make('final_amount.option')
                ->label('Final Amount Label')
                ->options(DocumentDefault::getAvailableFinalAmountNameOptions())
                ->visible(static fn(?DocumentDefault $record) => $record?->type !== DocumentType::Delivery)
                ->afterStateUpdated(static function (Get $get, Set $set, $state, $old) {
                    if ($state !== 'other' && $old === 'other' && filled($get('final_amount.custom'))) {
                        $set('final_amount.old_custom', $get('final_amount.custom'));
                        $set('final_amount.custom', null);
                    }
                    if ($state === 'other' && $old !== 'other') {
                        $set('final_amount.custom', $get('final_amount.old_custom'));
                    }
                }),
            Forms\Components\TextInput::make('final_amount.custom')
                ->hiddenLabel()
                ->visible(static fn(?DocumentDefault $record) => $record?->type !== DocumentType::Delivery)
                ->disabled(static fn(callable $get) => $get('final_amount.option') !== 'other')
                ->nullable(),

            // Champs spécifiques aux documents de livraison (Delivery)
            Forms\Components\Select::make('delivery_person.option')
                ->label('Delivery Label')
                ->options(DocumentDefault::getAvailableDeliveryPersonNameOptions())
                ->visible(static fn(?DocumentDefault $record) => $record?->type === DocumentType::Delivery)
                ->afterStateUpdated(static function (Get $get, Set $set, $state, $old) {
                    if ($state !== 'other' && $old === 'other' && filled($get('delivery_person.custom'))) {
                        $set('delivery_person.old_custom', $get('delivery_person.custom'));
                        $set('delivery_person.custom', null);
                    }
                    if ($state === 'other' && $old !== 'other') {
                        $set('delivery_person.custom', $get('delivery_person.old_custom'));
                    }
                }),
            Forms\Components\TextInput::make('delivery_person.custom')
                ->hiddenLabel()
                ->visible(static fn(?DocumentDefault $record) => $record?->type === DocumentType::Delivery)
                ->disabled(static fn(callable $get) => $get('delivery_person.option') !== 'other')
                ->nullable(),

            Forms\Components\Select::make('receiver_person.option')
                ->label('Receiver Label')
                ->options(DocumentDefault::getAvailableReceiverPersonNameOptions())
                ->visible(static fn(?DocumentDefault $record) => $record?->type === DocumentType::Delivery)
                ->afterStateUpdated(static function (Get $get, Set $set, $state, $old) {
                    if ($state !== 'other' && $old === 'other' && filled($get('receiver_person.custom'))) {
                        $set('receiver_person.old_custom', $get('receiver_person.custom'));
                        $set('receiver_person.custom', null);
                    }
                    if ($state === 'other' && $old !== 'other') {
                        $set('receiver_person.custom', $get('receiver_person.old_custom'));
                    }
                }),
            Forms\Components\TextInput::make('receiver_person.custom')
                ->hiddenLabel()
                ->visible(static fn(?DocumentDefault $record) => $record?->type === DocumentType::Delivery)
                ->disabled(static fn(callable $get) => $get('receiver_person.option') !== 'other')
                ->nullable(),

            Forms\Components\Select::make('remarks_name.option')
                ->label('Remarks Label')
                ->options(DocumentDefault::getAvailableRemarksNameOptions())
                ->visible(static fn(?DocumentDefault $record) => $record?->type === DocumentType::Delivery)
                ->afterStateUpdated(static function (Get $get, Set $set, $state, $old) {
                    if ($state !== 'other' && $old === 'other' && filled($get('remarks_name.custom'))) {
                        $set('remarks_name.old_custom', $get('remarks_name.custom'));
                        $set('remarks_name.custom', null);
                    }
                    if ($state === 'other' && $old !== 'other') {
                        $set('remarks_name.custom', $get('remarks_name.old_custom'));
                    }
                }),
            Forms\Components\TextInput::make('remarks_name.custom')
                ->hiddenLabel()
                ->visible(static fn(?DocumentDefault $record) => $record?->type === DocumentType::Delivery)
                ->disabled(static fn(callable $get) => $get('remarks_name.option') !== 'other')
                ->nullable(),
        ];
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->sortable(),
                Tables\Columns\TextColumn::make('number_prefix')
                    ->label('Prefix')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('header')
                    ->label('Header')
                    ->limit(30)
                    ->searchable(),
                Tables\Columns\TextColumn::make('template')
                    ->badge()
                    ->sortable(),
                Tables\Columns\IconColumn::make('show_logo')
                    ->label('Logo')
                    ->boolean(),
                Tables\Columns\TextColumn::make('font')
                    ->badge()
                    ->sortable(),
                Tables\Columns\ColorColumn::make('accent_color')
                    ->label('Color'),
                Tables\Columns\IconColumn::make('show_payment_details')
                    ->label('Payment')
                    ->boolean(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options(DocumentType::class),
                Tables\Filters\SelectFilter::make('template')
                    ->options(Template::class),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDocumentDefaults::route('/'),
            'edit' => Pages\EditDocumentDefault::route('/{record}/edit'),
        ];
    }
}
