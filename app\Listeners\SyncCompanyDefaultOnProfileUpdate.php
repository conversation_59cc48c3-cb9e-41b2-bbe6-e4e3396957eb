<?php

namespace App\Listeners;

use App\Models\CompanyProfile;
use App\Services\CompanyDefaultSyncService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class SyncCompanyDefaultOnProfileUpdate
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event when CompanyProfile is created.
     */
    public function created(CompanyProfile $profile): void
    {
        $this->syncCompanyDefault($profile, 'created');
    }

    /**
     * Handle the event when CompanyProfile is updated.
     */
    public function updated(CompanyProfile $profile): void
    {
        $this->syncCompanyDefault($profile, 'updated');
    }

    /**
     * Synchronise CompanyDefault lorsqu'un profil d'entreprise est modifié
     */
    private function syncCompanyDefault(CompanyProfile $profile, string $action): void
    {
        try {
            Log::info("Synchronisation CompanyDefault déclenchée par {$action} du profil d'entreprise (ID: {$profile->id}, Company: {$profile->company_id})");
            
            $syncService = app(CompanyDefaultSyncService::class);
            $syncService->syncFromCompanyProfile($profile);
            
            Log::info("Synchronisation CompanyDefault terminée pour le profil d'entreprise {$profile->id}");
            
        } catch (\Exception $e) {
            Log::error("Erreur lors de la synchronisation CompanyDefault pour le profil {$profile->id}: " . $e->getMessage());
        }
    }
}
