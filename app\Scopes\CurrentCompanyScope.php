<?php

namespace App\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class CurrentCompanyScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     */
    public function apply(Builder $builder, Model $model): void
    {
        $companyId = session('current_company_id');

        if (!$companyId && app()->runningInConsole()) {
            return;
        }

        if (!$companyId && Auth::check() && Auth::user()->currentCompany) {
            $companyId = Auth::user()->currentCompany->id;
            session(['current_company_id' => $companyId]);
        }

        if (!$companyId && Auth::check() && Auth::user()->currentCompany) {
            $companyId = Auth::user()->currentCompany->id;
        }

        if ($companyId) {
            $builder->where("{$model->getTable()}.company_id", $companyId);
        } else {
            // Si aucune entreprise courante n'est définie, retourner une requête vide
            // au lieu de lancer une exception. Cela permet aux pages de se charger
            // même si l'utilisateur n'a pas encore d'entreprise.
            Log::warning('CurrentCompanyScope: No company_id found for user ' . Auth::id());
            $builder->whereRaw('1 = 0'); // Requête qui ne retourne aucun résultat
        }
    }
}
