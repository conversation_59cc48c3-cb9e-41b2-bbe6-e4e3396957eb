<?php

namespace App\Providers;

use Closure;
use BackedEnum;
use Carbon\Carbon;
use App\Enums\DateFormat;
use Akaunting\Money\Money;
use Carbon\CarbonInterface;
use Filament\Support\RawJs;
use App\Models\Localization;
use Akaunting\Money\Currency;
use Illuminate\Support\HtmlString;
use App\Utilities\CurrencyAccessor;
use App\Enums\AdjustmentComputation;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Support\ServiceProvider;
use App\Services\CompanySettingsService;
use Filament\Forms\Components\TextInput;
use Filament\Support\Enums\IconPosition;
use Filament\Forms\Components\DatePicker;
use Filament\Actions\Exports\ExportColumn;
use Illuminate\Contracts\Support\Htmlable;
use Filament\Infolists\Components\TextEntry;

class MacroServiceProvider extends ServiceProvider
{
    public function boot(): void
    {

        TextInput::macro('money', function (string|Closure|null $currency = null, bool $useAffix = true): static {
            /** @var TextInput $this */
            $currency ??= CurrencyAccessor::getDefaultCurrency();

            if ($useAffix) {
                $this
                    ->prefix(static function (TextInput $component) use ($currency) {
                        $currency = $component->evaluate($currency);
                        return currency($currency)->getPrefix();
                    })
                    ->suffix(static function (TextInput $component) use ($currency) {
                        $currency = $component->evaluate($currency);
                        return currency($currency)->getSuffix();
                    });
            }

            $this->mask(RawJs::make('$money($input)'))
                ->afterStateHydrated(function (TextInput $component, $state) {
                    if (blank($state)) {
                        return;
                    }

                    // Maintenant que nous utilisons des valeurs décimales directement
                    $component->state(number_format((float) $state, 2, '.', ''));
                })
                ->dehydrateStateUsing(function (?string $state): ?float {
                    if (blank($state)) {
                        return null;
                    }

                    $cleaned = str_replace(',', '', $state);
                    return (float) $cleaned;
                });

            return $this;
        });

        TextInput::macro('rate', function (string|Closure|null $computation = null, string|Closure|null $currency = null, bool $showAffix = true): static {
            /** @var TextInput $this */
            return $this
                ->when(
                    $showAffix,
                    fn(TextInput $component) => $component
                        ->prefix(function (TextInput $component) use ($computation, $currency) {
                            $evaluatedComputation = $component->evaluate($computation);
                            $evaluatedCurrency = $component->evaluate($currency);
                            return ratePrefix($evaluatedComputation, $evaluatedCurrency);
                        })
                        ->suffix(function (TextInput $component) use ($computation, $currency) {
                            $evaluatedComputation = $component->evaluate($computation);
                            $evaluatedCurrency = $component->evaluate($currency);
                            return rateSuffix($evaluatedComputation, $evaluatedCurrency);
                        })
                )
                ->mask(static function (TextInput $component) use ($computation, $currency) {
                    $computation = $component->evaluate($computation);
                    $currency = $component->evaluate($currency);
                    $computationEnum = AdjustmentComputation::parse($computation);

                    return $computationEnum->isPercentage()
                        ? rateMask(computation: $computation)
                        : moneyMask($currency);
                })
                ->rule(static function (TextInput $component) use ($computation) {
                    return static function (string $attribute, $value, Closure $fail) use ($computation, $component) {
                        $computation = $component->evaluate($computation);
                        $numericValue = (float) $value;

                        if ($computation instanceof BackedEnum) {
                            $computation = $computation->value;
                        }

                        if (in_array($computation, ['percentage', 'compound']) && ($numericValue < 0 || $numericValue > 100)) {
                            $fail('The rate must be between 0 and 100.');
                        } elseif ($computation === 'fixed' && $numericValue < 0) {
                            $fail('The rate must be greater than 0.');
                        }
                    };
                });
        });

        TextColumn::macro('coloredDescription', function (string|Htmlable|Closure|null $description, string $color = 'danger'): static {
            /** @var TextColumn $this */
            return $this->description(static function (TextColumn $column) use ($description, $color): Htmlable {
                $evaluatedDescription = $column->evaluate($description);
                return new HtmlString("<span class='text-{$color}-700 dark:text-{$color}-400'>{$evaluatedDescription}</span>");
            });
        });

        TextColumn::macro('defaultDateFormat', function (): static {
            /** @var TextColumn $this */
            $localization = Localization::firstOrFail();
            $dateFormat = $localization->date_format->value ?? DateFormat::DEFAULT;
            $timezone = $localization->timezone ?? Carbon::now()->timezoneName;

            return $this->date($dateFormat, $timezone);
        });

        DatePicker::macro('defaultDateFormat', function (): static {
            /** @var DatePicker $this */
            $localization = Localization::firstOrFail();
            $dateFormat = $localization->date_format->value ?? DateFormat::DEFAULT;
            $timezone = $localization->timezone ?? Carbon::now()->timezoneName;

            return $this->displayFormat($dateFormat)->timezone($timezone);
        });

        TextColumn::macro('currency', function (string|Closure|null $currency = null, ?bool $convert = null): static {
            /** @var TextColumn $this */
            $currency ??= CurrencyAccessor::getDefaultCurrency();
            $convert ??= false;

            return $this->formatStateUsing(static function (TextColumn $column, $state) use ($currency, $convert): ?string {
                if (blank($state)) {
                    return null;
                }

                $currency = $column->evaluate($currency);
                $convert = $column->evaluate($convert);

                return money($state, $currency, $convert)->format();
            });
        });

        TextEntry::macro('currency', function (string|Closure|null $currency = null, ?bool $convert = null): static {
            /** @var TextEntry $this */
            $currency ??= CurrencyAccessor::getDefaultCurrency();
            $convert ??= false;

            return $this->formatStateUsing(static function (TextEntry $entry, $state) use ($currency, $convert): ?string {
                if (blank($state)) {
                    return null;
                }

                $currency = $entry->evaluate($currency);
                $convert = $entry->evaluate($convert);

                return money($state, $currency, $convert)->format();
            });
        });

        TextColumn::macro('rate', function (string|Closure|null $computation = null): static {
            /** @var TextColumn $this */
            return $this->formatStateUsing(static function (TextColumn $column, $state) use ($computation): ?string {
                $computation = $column->evaluate($computation);
                return rateFormat(state: $state, computation: $computation);
            });
        });

        TextEntry::macro('link', function (bool $condition = true): static {
            /** @var TextEntry $this */
            if ($condition) {
                $this
                    ->limit(50)
                    ->openUrlInNewTab()
                    ->icon('heroicon-o-arrow-top-right-on-square')
                    ->iconColor('primary')
                    ->iconPosition(IconPosition::After);
            }

            return $this;
        });

        Carbon::macro('toDefaultDateFormat', function () {
            $companyId = auth()->user()?->current_company_id;
            $dateFormat = CompanySettingsService::getDefaultDateFormat($companyId);
            $timezone = CompanySettingsService::getDefaultTimezone($companyId);

            return $this->setTimezone($timezone)->format($dateFormat);
        });



        TextColumn::macro('hideOnTabs', function (array $tabs): static {
            $this->toggleable(isToggledHiddenByDefault: function (HasTable $livewire) use ($tabs) {
                return in_array($livewire->activeTab, $tabs);
            });

            return $this;
        });

        TextColumn::macro('showOnTabs', function (array $tabs): static {
            $this->toggleable(isToggledHiddenByDefault: function (HasTable $livewire) use ($tabs) {
                return !in_array($livewire->activeTab, $tabs);
            });

            return $this;
        });

        TextColumn::macro('defaultDateFormat', function (): static {
            $localization = Localization::firstOrFail();

            $dateFormat = $localization->date_format->value ?? DateFormat::DEFAULT;
            $timezone = $localization->timezone ?? Carbon::now()->timezoneName;

            $this->date($dateFormat, $timezone);

            return $this;
        });

        DatePicker::macro('defaultDateFormat', function (): static {
            $localization = Localization::firstOrFail();

            $dateFormat = $localization->date_format->value ?? DateFormat::DEFAULT;
            $timezone = $localization->timezone ?? Carbon::now()->timezoneName;

            $this->displayFormat($dateFormat)
                ->timezone($timezone);

            return $this;
        });

        TextColumn::macro('asRelativeDay', function (?string $timezone = null): static {
            $this->formatStateUsing(function (TextColumn $column, mixed $state) use ($timezone) {
                if (blank($state)) {
                    return null;
                }

                $date = Carbon::parse($state)
                    ->setTimezone($timezone ?? $column->getTimezone());

                if ($date->isToday()) {
                    return 'Today';
                }

                return $date->diffForHumans([
                    'options' => CarbonInterface::ONE_DAY_WORDS,
                ]);
            });

            return $this;
        });

        ExportColumn::macro('date', function () {
            $this->formatStateUsing(static function ($state) {
                if (blank($state)) {
                    return null;
                }

                try {
                    return Carbon::parse($state)->toDateString();
                } catch (\Exception) {
                    return null;
                }
            });

            return $this;
        });

        ExportColumn::macro('dateTime', function () {
            $this->formatStateUsing(static function ($state) {
                if (blank($state)) {
                    return null;
                }

                try {
                    return Carbon::parse($state)->toDateTimeString();
                } catch (\Exception) {
                    return null;
                }
            });

            return $this;
        });

        ExportColumn::macro('enum', function () {
            $this->formatStateUsing(static function ($state) {
                if (blank($state)) {
                    return null;
                }

                if (!($state instanceof BackedEnum)) {
                    return $state;
                }

                if ($state instanceof HasLabel) {
                    return $state->getLabel();
                }

                return $state->value;
            });

            return $this;
        });


        Money::macro('formatWithCode', function (bool $codeBefore = false) {
            $formatted = $this->format();

            $currencyCode = $this->currency->getCurrency();

            if ($codeBefore) {
                return $currencyCode . ' ' . $formatted;
            }

            return $formatted . ' ' . $currencyCode;
        });

        Currency::macro('getEntity', function () {
            $currencyCode = $this->getCurrency();

            $entity = config("money.currencies.{$currencyCode}.entity");

            return $entity ?? $currencyCode;
        });

        Currency::macro('getCodePrefix', function () {
            if ($this->isSymbolFirst()) {
                return '';
            }

            return ' ' . $this->getCurrency();
        });

        Currency::macro('getCodeSuffix', function () {
            if ($this->isSymbolFirst()) {
                return ' ' . $this->getCurrency();
            }

            return '';
        });

        Carbon::macro('toDefaultDateFormat', function () {
            $companyId = auth()->user()?->current_company_id;
            $dateFormat = CompanySettingsService::getDefaultDateFormat($companyId);
            $timezone = CompanySettingsService::getDefaultTimezone($companyId);

            return $this->setTimezone($timezone)->format($dateFormat);
        });

        ExportColumn::macro('money', function () {
            $this->formatStateUsing(static function ($state) {
                if (blank($state) || !is_numeric($state)) {
                    return 0.00;
                }
                // Maintenant que nous utilisons des valeurs décimales directement
                return (float) $state;
            });

            return $this;
        });

        ExportColumn::macro('date', function () {
            $this->formatStateUsing(static function ($state) {
                if (blank($state)) {
                    return null;
                }

                try {
                    return Carbon::parse($state)->toDateString();
                } catch (\Exception) {
                    return null;
                }
            });

            return $this;
        });

    }
}
