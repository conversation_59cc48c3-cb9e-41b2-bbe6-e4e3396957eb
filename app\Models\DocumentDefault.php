<?php

namespace App\Models;

use App\Traits\Blamable;
use App\Enums\DocumentType;
use App\Enums\DocumentDiscountMethod;
use App\Enums\Font;
use App\Enums\Template;
use App\Traits\CompanyOwned;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Builder;
use Database\Factories\DocumentDefaultFactory;
use Illuminate\Database\Eloquent\Casts\AsArrayObject;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class DocumentDefault extends Model
{
    use HasFactory;
    use Blamable;
    use CompanyOwned;

    protected $table = 'document_defaults';

    protected static function boot()
    {
        parent::boot();

        static::saving(function (DocumentDefault $model) {
            $model->cleanFields();
        });
    }


    protected $fillable = [
        'company_id',
        'type',
        'logo',
        'qr_code',
        'qr_options',
        'show_logo',
        'show_qr_code',
        'number_prefix',
        'header',
        'subheader',
        'client_name',
        'client_register',
        'client_address',
        'object_name',
        'object_key_value',
        'footer',
        'accent_color',
        'font',
        'template',
        'currency_code',
        'notes',
        'text_with_amount_in_words',
        'show_header',
        'show_client_details',
        'show_objet_details',
        'show_company_details',
        'show_lines',
        'show_totals',
        'show_discount',
        'show_tax',
        'show_payment_details',
        'show_notes',
        'show_signature',
        'show_footer',
        'discount_method',
        'payment_terms',
        'final_amount',
        'authorized_personnel_signature',
        'authorized_personnel_signature_name',
        'authorized_personnel_signature_role',
        'payment',
        'item_name',
        'unit_name',
        'price_name',
        'amount_name',
        'delivery_person',
        'receiver_person',
        'remarks',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'type' => DocumentType::class,
        'show_logo' => 'boolean',
        'show_qr_code' => 'boolean',
        'show_header' => 'boolean',
        'show_client_details' => 'boolean',
        'show_objet_details' => 'boolean',
        'show_company_details' => 'boolean',
        'show_lines' => 'boolean',
        'show_totals' => 'boolean',
        'show_discount' => 'boolean',
        'show_tax' => 'boolean',
        'show_payment_details' => 'boolean',
        'show_notes' => 'boolean',
        'show_signature' => 'boolean',
        'show_footer' => 'boolean',
        'discount_method' => DocumentDiscountMethod::class,
        'payment_terms' => \App\Enums\PaymentTerms::class,
        'font' => Font::class,
        'template' => Template::class,
        'client_name' => AsArrayObject::class,
        'client_register' => AsArrayObject::class,
        'client_address' => AsArrayObject::class,
        'object_name' => AsArrayObject::class,
        'object_key_value' => AsArrayObject::class,
        'delivery_date' => 'date',
        'issue_date' => 'date',
        'due_date_' => 'date',
        'authorized_personnel_signature_name' => AsArrayObject::class,
        'authorized_personnel_signature_role' => AsArrayObject::class,
        'payment' => AsArrayObject::class,
        'item_name' => AsArrayObject::class,
        'unit_name' => AsArrayObject::class,
        'price_name' => AsArrayObject::class,
        'amount_name' => AsArrayObject::class,
        'final_amount' => AsArrayObject::class,
        'delivery_person' => AsArrayObject::class,
        'receiver_person' => AsArrayObject::class,
        'remarks' => AsArrayObject::class,
        'qr_options' => 'array'
    ];

    protected $appends = [
        'logo_url',
    ];

    protected function logoUrl(): Attribute
    {
        return Attribute::get(static function (mixed $value, array $attributes): ?string {
            return $attributes['logo']
                ? Storage::disk('public')->url($attributes['logo'])
                : null;
        });
    }

    public function scopeType(Builder $query, string|DocumentType $type): Builder
    {
        return $query->where($this->qualifyColumn('type'), $type);
    }

    public function scopeInvoice(Builder $query): Builder
    {
        return $query->type(DocumentType::Invoice);
    }

    public function scopeEstimate(Builder $query): Builder
    {
        return $query->type(DocumentType::Estimate);
    }

    public function scopeDelivery(Builder $query): Builder
    {
        return $query->type(DocumentType::Delivery);
    }

    public function scopeBill(Builder $query): Builder
    {
        return $query->type(DocumentType::Bill);
    }

    public function getNumberNext(?string $prefix = null, int|string|null $next = null): string
    {
        $numberPrefix = $prefix ?? $this->number_prefix ?? '';
        $numberNext = (string) ($next ?? (static::getBaseNumber() + 1));

        return $numberPrefix . $numberNext;
    }

    public static function getBaseNumber(): int
    {
        return 100000;
    }

    public static function getAvailableItemNameOptions(): array
    {
        return [
            'items' => 'Items',
            'products' => 'Products',
            'services' => 'Services',
            'other' => 'Other',
        ];
    }

    public static function getAvailableUnitNameOptions(): array
    {
        return [
            'quantity' => 'Quantity',
            'hours' => 'Hours',
            'other' => 'Other',
        ];
    }

    public static function getAvailablePriceNameOptions(): array
    {
        return [
            'price' => 'Price',
            'rate' => 'Rate',
            'other' => 'Other',
        ];
    }

    public static function getAvailableAmountNameOptions(): array
    {
        return [
            'amount' => 'Amount',
            'total' => 'Total',
            'other' => 'Other',
        ];
    }

    public static function getAvailableFinalAmountNameOptions(): array
    {
        return [
            'total' => 'Total',
            'amount' => 'Amount',
            'other' => 'Other',
        ];
    }

    public static function getAvailableDeliveryPersonNameOptions(): array
    {
        return [
            'delivery' => 'Delivery',
            'shipment' => 'Shipment',
            'other' => 'Other',
        ];
    }

    public static function getAvailableReceiverPersonNameOptions(): array
    {
        return [
            'receiver' => 'Receiver',
            'recipient' => 'Recipient',
            'other' => 'Other',
        ];
    }

    public static function getAvailableRemarksNameOptions(): array
    {
        return [
            'remarks' => 'Remarks',
            'notes' => 'Notes',
            'other' => 'Other',
        ];
    }

    public static function getAvailableClientNameOptions(): array
    {
        return [
            'client' => 'Client',
            'customer' => 'Customer',
            'other' => 'Other',
        ];
    }

    public static function getAvailableClientAddressNameOptions(): array
    {
        return [
            'address' => 'Address',
            'location' => 'Location',
            'other' => 'Other',
        ];
    }

    public static function getAvailableClientTaxIDNameOptions(): array
    {
        return [
            'tax_id' => 'Tax ID',
            'fiscal_id' => 'Fiscal ID',
            'other' => 'Other',
        ];
    }

    public static function getAvailableDocumentObjectNameOptions(): array
    {
        return [
            'object' => 'Object',
            'subject' => 'Subject',
            'other' => 'Other',
        ];
    }

    public static function getAvailableDocumentObjectValueNameOptions(): array
    {
        return [
            'sale' => 'Sale Of',
            'deliver-of' => 'Deliver Of',
            'service' => 'Service Of',
            'other' => 'Other',
        ];
    }

    public static function getAvailableAuthorizedPersonnelSignatureNameOptions(): array
    {
        return [
            'manager' => 'Manager',
            'director' => 'Director',
            'other' => 'Other',
        ];
    }

    public static function getAvailableAuthorizedPersonnelSignatureRoleNameOptions(): array
    {
        return [
            'director' => 'Director',
            'manager' => 'Manager',
            'other' => 'Other',
        ];
    }

    public static function getAvailablePaymentTypeNameOptions(): array
    {
        return [
            'payment' => 'Payment',
            'method' => 'Method',
            'other' => 'Other',
        ];
    }


    public function getLabelOptionFor(string $optionType, ?string $optionValue)
    {
        $optionValue = $optionValue ?? ($this->{$optionType}['option'] ?? null);

        if (!$optionValue) {
            return null;
        }


        $options = match ($optionType) {
            'item_name' => static::getAvailableItemNameOptions(),
            'unit_name' => static::getAvailableUnitNameOptions(),
            'price_name' => static::getAvailablePriceNameOptions(),
            'amount_name' => static::getAvailableAmountNameOptions(),
            'final_amount' => static::getAvailableFinalAmountNameOptions(),
            'delivery_person' => static::getAvailableDeliveryPersonNameOptions(),
            'receiver_person' => static::getAvailableReceiverPersonNameOptions(),
            'remarks' => static::getAvailableRemarksNameOptions(),
            'client_name' => static::getAvailableClientNameOptions(),
            'client_register' => static::getAvailableClientTaxIDNameOptions(),
            'client_address' => static::getAvailableClientAddressNameOptions(),
            'object_name' => static::getAvailableDocumentObjectNameOptions(),
            'object_key_value' => static::getAvailableDocumentObjectValueNameOptions(),
            'authorized_personnel_signature_name' => static::getAvailableAuthorizedPersonnelSignatureNameOptions(),
            'authorized_personnel_signature_role' => static::getAvailableAuthorizedPersonnelSignatureRoleNameOptions(),
            default => [],
        };

        return $options[$optionValue] ?? null;
    }


    public function resolveColumnLabel(string $column, string $default, ?array $data = null): string
    {

        if ($data) {
            $custom = $data[$column]['custom'] ?? null;
            $option = $data[$column]['option'] ?? null;
        } else {
            $columnData = $this->{$column} ?? [];
            $custom = $columnData['custom'] ?? null;
            $option = $columnData['option'] ?? null;
        }


        if ($custom) {
            return $custom;
        }


        return $this->getLabelOptionFor($column, $option) ?? $default;
    }

    public function isDelivery(): bool
    {
        return $this->type === DocumentType::Delivery;
    }

    public function getFields(): array
    {
        $base = ['item_name'];

        if ($this->isDelivery()) {
            return array_merge($base, [
                'delivery_person',
                'receiver_person',
                'remarks',
            ]);
        }

        return array_merge($base, [
            'unit_name',
            'price_name',
            'amount_name',
            'final_amount',
        ]);
    }

    public function hasField(string $field): bool
    {
        return in_array($field, $this->getFields());
    }

    public static function getDeliveryFields(): array
    {
        return [
            'delivery_person',
            'receiver_person',
            'remarks',
        ];
    }

    public static function getStandardFields(): array
    {
        return [
            'unit_name',
            'price_name',
            'amount_name',
            'final_amount',
        ];
    }

    public function cleanFields(): void
    {
        if ($this->isDelivery()) {
            foreach (self::getStandardFields() as $field) {
                $this->attributes[$field] = null;
            }
        } else {
            foreach (self::getDeliveryFields() as $field) {
                $this->attributes[$field] = null;
            }
        }
    }

    public function validate(): bool
    {
        $hasErrors = false;

        if ($this->isDelivery()) {
            foreach (self::getStandardFields() as $field) {
                if (!empty($this->attributes[$field])) {
                    Notification::make()
                        ->title('Champ non autorisé')
                        ->body("Le champ {$field} n'est pas autorisé pour les documents de livraison.")
                        ->danger()
                        ->send();
                    $hasErrors = true;
                }
            }
        } else {
            foreach (self::getDeliveryFields() as $field) {
                if (!empty($this->attributes[$field])) {
                    Notification::make()
                        ->title('Champ non autorisé')
                        ->body("Le champ {$field} n'est pas autorisé pour les documents de type {$this->type->value}.")
                        ->danger()
                        ->send();
                    $hasErrors = true;
                }
            }
        }

        if (!$hasErrors) {
            Notification::make()
                ->title('Validation réussie')
                ->body('Tous les champs sont valides pour ce type de document.')
                ->success()
                ->send();
        }

        return !$hasErrors;
    }

    public function getLabels(): array
    {
        $labels = [];
        $fields = $this->getFields();

        foreach ($fields as $field) {
            $label = $this->resolveColumnLabel($field, ucfirst(str_replace('_', ' ', $field)));
            $labels[$field] = $label;
        }

        return $labels;
    }

    public function getDefaults(): array
    {
        $defaults = [
            'item_name' => ['option' => 'items', 'custom' => null],
        ];

        if ($this->isDelivery()) {
            $defaults = array_merge($defaults, [
                'delivery_person' => ['option' => 'delivery', 'custom' => null],
                'receiver_person' => ['option' => 'receiver', 'custom' => null],
                'remarks' => ['option' => 'remarks', 'custom' => null],
            ]);
        } else {
            $defaults = array_merge($defaults, [
                'unit_name' => ['option' => 'quantity', 'custom' => null],
                'price_name' => ['option' => 'price', 'custom' => null],
                'amount_name' => ['option' => 'amount', 'custom' => null],
                'final_amount' => ['option' => 'total', 'custom' => null],
            ]);
        }

        return $defaults;
    }

    public function applyDefaults(): void
    {
        $defaults = $this->getDefaults();

        foreach ($defaults as $field => $value) {
            if (empty($this->attributes[$field])) {
                $this->attributes[$field] = $value;
            }
        }
    }

    protected static function newFactory(): Factory
    {
        return DocumentDefaultFactory::new();
    }
}
