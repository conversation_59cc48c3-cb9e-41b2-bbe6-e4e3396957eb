<?php

namespace App\Filament\Company\Pages;


use Filament\Forms\Form;
use Filament\Pages\Page;
use App\Enums\EntityType;
use App\Enums\AddressType;
use App\Utilities\Timezone;
use Filament\Actions\Action;
use Livewire\Attributes\Locked;
use function Filament\authorize;
use Filament\Actions\ActionGroup;
use Filament\Forms\Components\Group;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Support\Exceptions\Halt;
use Filament\Forms\Components\Section;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use App\Filament\Forms\Components\Banner;
use Filament\Forms\Components\FileUpload;
use Illuminate\Contracts\Support\Htmlable;
use App\Filament\Company\Clusters\Settings;
use App\Filament\Forms\Components\AddressFields;
use Illuminate\Auth\Access\AuthorizationException;
use App\Models\CompanyProfile as CompanyProfileModel;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;


class CompanyProfile extends Page
{
    use InteractsWithFormActions;

    protected static ?string $cluster = Settings::class;

    //protected static ?string $navigationIcon = 'heroicon-o-squares-2x2';
    protected static ?string $title = 'Company Profile';
    protected static string $view = 'filament.company.pages.company-profile';
    public ?array $data = [];

    #[Locked]
    public ?CompanyProfileModel $record = null;

    public function getTitle(): string|Htmlable
    {
        return (static::$title);
    }

    public static function getNavigationLabel(): string
    {
        return static::$title;
    }

    public function getMaxContentWidth(): MaxWidth|string|null
    {
        return MaxWidth::ScreenTwoExtraLarge;
    }

    public function mount(): void
    {
        $this->record = CompanyProfileModel::firstOrNew([
            'company_id' => auth()->user()->current_company_id,
        ]);

        abort_unless(static::canView($this->record), 404);

        $this->fillForm();
    }

    public function fillForm(): void
    {
        $data = $this->record->attributesToArray();

        $this->form->fill($data);
    }

    public function save(): void
    {
        try {
            $data = $this->form->getState();

            $this->handleRecordUpdate($this->record, $data);
        } catch (Halt $exception) {
            return;
        }

        $this->getSavedNotification()->send();
    }

    protected function updateTimezone(string $countryCode): void
    {
        $model = \App\Models\Localization::firstOrFail();

        $timezones = Timezone::getTimezonesForCountry($countryCode);

        if (!empty($timezones)) {
            $model->update([
                'timezone' => $timezones[0],
            ]);
        }
    }

    protected function getTimezoneChangeNotification(): Notification
    {
        return Notification::make()
            ->info()
            ->title('Timezone update required')
            ->body('You have changed your country or state. Please update your timezone to ensure accurate date and time information.')
            ->actions([
                \Filament\Notifications\Actions\Action::make('updateTimezone')
                    ->label('Update timezone')
                    ->url(Localization::getUrl()),
            ])
            ->persistent()
            ->send();
    }

    protected function getSavedNotification(): Notification
    {
        return Notification::make()
            ->success()
            ->title(__('filament-panels::resources/pages/edit-record.notifications.saved.title'));
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                $this->getIdentificationSection(),
                $this->getNeedsAddressCompletionAlert(),
                $this->getLocationDetailsSection(),
                $this->getLegalAndComplianceSection(),
            ])
            ->model($this->record)
            ->statePath('data')
            ->operation('edit');
    }

    protected function getIdentificationSection(): Component
    {
        return Section::make('Identification')
            ->schema([
                Group::make()
                    ->schema([
                        TextInput::make('email')
                            ->email()
                            ->label('Email')
                            ->maxLength(255)
                            ->required(),
                        TextInput::make('phone_number')
                            ->tel()
                            ->label('Phone'),
                    ])->columns(1),
                FileUpload::make('logo')
                    ->openable()
                    ->maxSize(2048)
                    ->label('')
                    ->visibility('public')
                    ->disk('public')
                    ->directory('logos/company')
                    ->imageResizeMode('contain')
                    ->imageCropAspectRatio('1:1')
                    ->panelAspectRatio('1:1')
                    ->panelLayout('integrated')
                    ->removeUploadedFileButtonPosition('center bottom')
                    ->uploadButtonPosition('center bottom')
                    ->uploadProgressIndicatorPosition('center bottom')
                    ->getUploadedFileNameForStorageUsing(
                        static fn(TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                            ->prepend((Auth::user()->currentCompany?->id ?? 'temp') . '_'),
                    )
                    ->extraAttributes(['class' => 'w-32 h-32'])
                    ->acceptedFileTypes(['image/png', 'image/jpeg']),
            ])->columns();
    }

    protected function getNeedsAddressCompletionAlert(): Component
    {
        return Banner::make('needsAddressCompletion')
            ->warning()
            ->title('Address information incomplete')
            ->description('Please complete the required address information for proper business operations.')
            ->visible(fn(CompanyProfileModel $record) => $record->address?->isIncomplete() ?? false)
            ->columnSpanFull();
    }

    protected function getLocationDetailsSection(): Component
    {
        return Section::make('Address Information')
            ->relationship('address')
            ->schema([
                Hidden::make('type')
                    ->default(AddressType::General),
                AddressFields::make()
                    ->required()
                    ->disabledCountry(false),
            ])
            ->columns(2)
            ->mutateRelationshipDataBeforeCreateUsing(function (array $data): array {
                $data['type'] = AddressType::General;
                return $data;
            })
            ->mutateRelationshipDataBeforeSaveUsing(function (array $data): array {
                $data['type'] = AddressType::General;
                return $data;
            });
    }

    protected function getLegalAndComplianceSection(): Component
    {
        return Section::make('Legal & Compliance')
            ->schema([
                Select::make('entity_type')
                    ->label('Entity Type')
                    ->options(EntityType::class)
                    ->required(),
                TextInput::make('tax_id')
                    ->label('Tax ID')
                    ->maxLength(50),
                TextInput::make('rccm')
                    ->label('RCCM')
                    ->maxLength(50),
                TextInput::make('currency_code')
                    ->label('Currency Code')
                    ->default(\App\Utilities\CurrencyAccessor::getDefaultCurrency())
                    ->disabled()
                    ->dehydrated()
                    ->required(),
                TextInput::make('fiscal_number')
                    ->label('Fiscal Number')
                    ->maxLength(50),
                TextInput::make('siret_number')
                    ->label('Siret number')
                    ->maxLength(50),
            ])->columns();
    }

    protected function handleRecordUpdate(CompanyProfileModel $record, array $data): CompanyProfileModel
    {
        // Séparer les données d'adresse des autres données
        $addressData = $data['address'] ?? [];
        unset($data['address']);

        // Mettre à jour le CompanyProfile
        $record->fill($data);

        $keysToWatch = [
            'logo',
        ];

        if ($record->isDirty($keysToWatch)) {
            $this->dispatch('companyProfileUpdated');
        }

        $record->save();

        // Gérer l'adresse
        if (!empty($addressData)) {
            // Assurer que le type est défini
            $addressData['type'] ??= AddressType::General;

            if ($record->address) {
                // Mettre à jour l'adresse existante
                $record->address->update($addressData);
            } else {
                // Créer une nouvelle adresse
                $record->address()->create($addressData);
            }
        }

        return $record;
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Charger les données d'adresse si elles existent
        if ($this->record->address) {
            $data['address'] = $this->record->address->toArray();
        }

        return $data;
    }

    /**
     * @return array<Action | ActionGroup>
     */
    protected function getFormActions(): array
    {
        return [
            $this->getSaveFormAction(),
        ];
    }

    protected function getSaveFormAction(): Action
    {
        return Action::make('save')
            ->label(__('filament-panels::resources/pages/edit-record.form.actions.save.label'))
            ->submit('save')
            ->keyBindings(['mod+s']);
    }

    public static function canView(Model $record): bool
    {
        try {
            return authorize('update', $record)->allowed();
        } catch (AuthorizationException $exception) {
            return $exception->toResponse()->allowed();
        }
    }
}
