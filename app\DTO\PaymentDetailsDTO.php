<?php

namespace App\DTO;

use App\Enums\PaymentMethod;
use App\Models\Transaction;
use App\Utilities\CurrencyConverter;

readonly class PaymentDetailsDTO
{
    public function __construct(
        public string $paymentType,
        public string $status,
        public string $amount,
        public string $currencyCode,
    ) {
    }

    public static function fromTransaction(Transaction $transaction): self
    {
        return new self(
            paymentType: $transaction->payment_method?->getLabel() ?? 'Non spécifié',
            status: $transaction->is_payment ? 'Payé' : 'En attente',
            amount: self::formatToMoney($transaction->amount, $transaction->transactionable->currency_code ?? 'USD'),
            currencyCode: $transaction->transactionable->currency_code ?? 'USD',
        );
    }

    public static function fake(): self
    {
        return new self(
            paymentType: 'Virement bancaire',
            status: 'Payé',
            amount: '$950.00',
            currencyCode: 'USD',
        );
    }

    protected static function formatToMoney(float|string|int $value, ?string $currencyCode): string
    {
        return CurrencyConverter::formatToMoney($value, $currencyCode);
    }

    public function toArray(): array
    {
        return [
            'payment_type' => $this->paymentType,
            'status' => $this->status,
            'amount' => $this->amount,
            'currency_code' => $this->currencyCode,
        ];
    }
}
