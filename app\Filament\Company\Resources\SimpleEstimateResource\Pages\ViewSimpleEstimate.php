<?php

namespace App\Filament\Company\Resources\SimpleEstimateResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use App\Filament\Company\Resources\SimpleEstimateResource;

class ViewSimpleEstimate extends ViewRecord
{
    protected static string $resource = SimpleEstimateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
