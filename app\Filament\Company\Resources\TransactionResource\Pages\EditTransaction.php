<?php

namespace App\Filament\Company\Resources\TransactionResource\Pages;

use Filament\Actions;
use App\Traits\HandlePageRedirect;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Company\Resources\TransactionResource;

class EditTransaction extends EditRecord
{
    use HandlePageRedirect;

    protected static string $resource = TransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
