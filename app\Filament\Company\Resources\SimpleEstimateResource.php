<?php

namespace App\Filament\Company\Resources;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Enums\DocumentType;
use App\Enums\EstimateStatus;
use App\Models\SimpleEstimate;
use Filament\Resources\Resource;
use Awcodes\TableRepeater\Header;
use App\Utilities\CurrencyAccessor;
use Illuminate\Support\Facades\Auth;
use App\Enums\DocumentDiscountMethod;
use App\Filament\Forms\Components\ClientSelect;
use App\Filament\Forms\Components\CurrencySelect;
use App\Filament\Forms\Components\DocumentTotals;
use App\Filament\Forms\Components\CustomTableRepeater;
use App\Filament\Forms\Components\DocumentHeaderSection;
use App\Services\NumberFormattingService;
use App\Filament\Company\Resources\SimpleEstimateResource\Pages;

class SimpleEstimateResource extends Resource
{
    protected static ?string $model = SimpleEstimate::class;

    protected static ?string $navigationLabel = 'Simple Estimates';

    protected static ?string $modelLabel = 'Simple Estimate';

    protected static ?string $pluralModelLabel = 'Simple Estimates';

    protected static ?string $navigationGroup = 'Simple Documents';

    public static function form(Form $form): Form
    {
        $settings = Auth::user()?->currentCompany?->defaultEstimate;

        return $form
            ->schema([
                DocumentHeaderSection::make('Estimate Header')
                    ->defaultHeader($settings?->header)
                    ->defaultSubheader($settings?->subheader),
                Forms\Components\Section::make('Estimate Details')
                    ->schema([
                        Forms\Components\Split::make([
                            Forms\Components\Group::make([
                                ClientSelect::make('client_id')
                                    ->label('Client')
                                    ->required()
                                    ->live()
                                    ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get, $state) {
                                        if (!$state) {
                                            return;
                                        }
                                        // Auto-set currency from client if available
                                        $client = \App\Models\Client::find($state);
                                        if ($client?->currency_code) {
                                            $set('currency_code', $client->currency_code);
                                        }
                                    }),
                                CurrencySelect::make('currency_code'),
                            ]),
                            Forms\Components\Group::make([
                                Forms\Components\TextInput::make('estimate_number')
                                    ->label('Estimate number')
                                    ->default(static fn() => SimpleEstimate::getNextDocumentNumber())
                                    ->required()
                                    ->maxLength(50)
                                    ->unique(SimpleEstimate::class, 'estimate_number', ignoreRecord: true)
                                    ->regex('/^[A-Za-z0-9\-_]+$/')
                                    ->validationMessages([
                                        'unique' => 'This estimate number already exists.',
                                        'regex' => 'Estimate number can only contain letters, numbers, hyphens, and underscores.',
                                    ]),
                                Forms\Components\DatePicker::make('date')
                                    ->label('Estimate date')
                                    ->live()
                                    ->default(now())
                                    ->required()
                                    ->maxDate(now()->addDays(30))
                                    ->minDate(now()->subYears(2)),
                                Forms\Components\Select::make('status')
                                    ->label('Status')
                                    ->options(EstimateStatus::class)
                                    ->default(EstimateStatus::Draft)
                                    ->required(),
                                Forms\Components\Select::make('discount_method')
                                    ->label('Discount method')
                                    ->options(DocumentDiscountMethod::class)
                                    ->required()
                                    ->default(DocumentDiscountMethod::PerDocument)
                                    ->afterStateUpdated(function ($state, Forms\Set $set) {
                                        $discountMethod = DocumentDiscountMethod::parse($state);
                                        if ($discountMethod?->isPerDocument()) {
                                            $set('lineItems.*.salesDiscounts', []);
                                        }
                                    })
                                    ->live(),
                            ])->grow(true),
                        ])->from('md'),
                        CustomTableRepeater::make('lineItems')
                            ->hiddenLabel()
                            ->relationship('lineItems')
                            ->saveRelationshipsUsing(null)
                            ->dehydrated(true)
                            ->reorderable()
                            ->orderColumn('line_number')
                            ->reorderAtStart()
                            ->cloneable()
                            ->addActionLabel('Add an item')
                            ->headers(function (Forms\Get $get) use ($settings) {
                                $discountMethod = DocumentDiscountMethod::parse($get('discount_method'));
                                $hasDiscounts = $discountMethod?->isPerLineItem() ?? false;

                                $headers = [
                                    Header::make($settings?->resolveColumnLabel('item_name', 'Item') ?? 'Item')->width('30%'),
                                    Header::make($settings?->resolveColumnLabel('quantity_name', 'Quantity') ?? 'Quantity')->width('10%'),
                                    Header::make($settings?->resolveColumnLabel('unit_price', 'Price') ?? 'Price')->width('15%'),
                                ];

                                if ($hasDiscounts) {
                                    $headers[] = Header::make('Adjustments')->width('30%');
                                } else {
                                    $headers[] = Header::make('Taxes')->width('30%');
                                }

                                $headers[] = Header::make($settings?->resolveColumnLabel('amount_name', 'Amount') ?? 'Amount')
                                    ->width('10%')
                                    ->align('right');

                                return $headers;
                            })
                            ->schema([
                                Forms\Components\Group::make([
                                    Forms\Components\TextInput::make('offering_name')
                                        ->label('Item')
                                        ->hiddenLabel()
                                        ->placeholder('Enter item name')
                                        ->required(),
                                    Forms\Components\Textarea::make('description')
                                        ->placeholder('Enter item description')
                                        ->hiddenLabel(),
                                ])->columnSpan(1),
                                Forms\Components\TextInput::make('quantity')
                                    ->required()
                                    ->numeric()
                                    ->live()
                                    ->minValue(0.01)
                                    ->maxValue(999999.99)
                                    ->step(0.01)
                                    ->default(1)
                                    ->suffix('pcs')
                                    ->validationMessages([
                                        'min' => 'Quantity must be greater than 0.',
                                        'max' => 'Quantity cannot exceed 999,999.99.',
                                        'numeric' => 'Quantity must be a valid number.',
                                    ]),
                                Forms\Components\TextInput::make('unit_price')
                                    ->hiddenLabel()
                                    ->required()
                                    ->numeric()
                                    ->step(0.01)
                                    ->live()
                                    ->minValue(0)
                                    ->maxValue(999999999.99)
                                    ->default(0)
                                    ->validationMessages([
                                        'min' => 'Unit price cannot be negative.',
                                        'max' => 'Unit price cannot exceed 999,999,999.99.',
                                        'numeric' => 'Unit price must be a valid number.',
                                    ]),
                                Forms\Components\Group::make([
                                    Forms\Components\TextInput::make('taxes')
                                        ->label('Taxes')
                                        ->hiddenLabel()
                                        ->placeholder('Enter taxes')
                                        ->suffix('%')
                                        ->prefix('Tax:'),
                                    Forms\Components\TextInput::make('discounts')
                                        ->label('Discounts')
                                        ->hiddenLabel()
                                        ->placeholder('Enter discounts')
                                        ->suffix('%')
                                        ->prefix('Disc:')
                                        ->visible(function (Forms\Get $get) {
                                            $discountMethod = DocumentDiscountMethod::parse($get('../../discount_method'));
                                            return $discountMethod?->isPerLineItem() ?? false;
                                        }),
                                ])->columnSpan(1),

                                Forms\Components\Placeholder::make('line_total')
                                    ->hiddenLabel()
                                    ->extraAttributes(['class' => 'text-right font-semibold'])
                                    ->content(function (Forms\Get $get) {
                                        $quantity = (float) ($get('quantity') ?? 0);
                                        $unitPrice = (float) ($get('unit_price') ?? 0);
                                        $total = $quantity * $unitPrice;
                                        $currencyCode = CurrencyAccessor::getDefaultCurrency();
                                        return NumberFormattingService::formatMoney($total, $currencyCode);
                                    })
                                    ->live(),
                            ]),

                        DocumentTotals::make()
                            ->type(DocumentType::Estimate),
                    ]),

                Forms\Components\Section::make('Additional Information')
                    ->schema([
                        Forms\Components\Textarea::make('notes')
                            ->label('Notes')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->searchable(),
                Tables\Columns\TextColumn::make('date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('estimate_number')
                    ->label('Number')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('client.name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('total')
                    ->currency(static fn(SimpleEstimate $record) => $record->currency_code)
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options(EstimateStatus::class),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSimpleEstimates::route('/'),
            'create' => Pages\CreateSimpleEstimate::route('/create'),
            'view' => Pages\ViewSimpleEstimate::route('/{record}'),
            'edit' => Pages\EditSimpleEstimate::route('/{record}/edit'),
        ];
    }
}
