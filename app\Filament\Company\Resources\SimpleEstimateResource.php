<?php

namespace App\Filament\Company\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\SimpleEstimate;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use App\Utilities\CurrencyAccessor;
use App\Enums\EstimateStatus;
use App\Filament\Forms\Components\ClientSelect;
use App\Filament\Forms\Components\CurrencySelect;
use App\Filament\Company\Resources\SimpleEstimateResource\Pages;

class SimpleEstimateResource extends Resource
{
    protected static ?string $model = SimpleEstimate::class;

    protected static ?string $navigationLabel = 'Simple Estimates';

    protected static ?string $modelLabel = 'Simple Estimate';

    protected static ?string $pluralModelLabel = 'Simple Estimates';

    protected static ?string $navigationGroup = 'Simple Documents';

    public static function form(Form $form): Form
    {

        return $form
            ->schema([
                Forms\Components\Section::make('Estimate Information')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                ClientSelect::make('client_id')
                                    ->label('Client')
                                    ->required(),
                                CurrencySelect::make('currency_code')
                                    ->default(CurrencyAccessor::getDefaultCurrency()),
                                Forms\Components\TextInput::make('estimate_number')
                                    ->label('Estimate Number')
                                    ->default(fn() => SimpleEstimate::getNextDocumentNumber())
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('reference_number')
                                    ->label('Reference Number')
                                    ->maxLength(255),
                                Forms\Components\DatePicker::make('date')
                                    ->label('Estimate Date')
                                    ->default(company_today())
                                    ->required(),
                                Forms\Components\DatePicker::make('expiration_date')
                                    ->label('Expiration Date')
                                    ->default(company_today()->addDays(30))
                                    ->required(),
                            ]),
                    ]),

                Forms\Components\Section::make('Line Items')
                    ->schema([
                        Forms\Components\Repeater::make('lineItems')
                            ->relationship()
                            ->schema([
                                Forms\Components\Grid::make(5)
                                    ->schema([
                                        Forms\Components\Textarea::make('description')
                                            ->label('Description')
                                            ->required()
                                            ->rows(2)
                                            ->columnSpan(2),
                                        Forms\Components\TextInput::make('quantity')
                                            ->label('Quantity')
                                            ->numeric()
                                            ->default(1)
                                            ->required()
                                            ->live()
                                            ->columnSpan(1),
                                        Forms\Components\TextInput::make('unit_price')
                                            ->label('Unit Price')
                                            ->numeric()
                                            ->step(0.01)
                                            ->required()
                                            ->live()
                                            ->columnSpan(1),
                                        Forms\Components\Placeholder::make('line_total')
                                            ->label('Total')
                                            ->content(function (Forms\Get $get) {
                                                $quantity = (float) ($get('quantity') ?? 0);
                                                $unitPrice = (float) ($get('unit_price') ?? 0);
                                                $total = $quantity * $unitPrice;
                                                $currencyCode = CurrencyAccessor::getDefaultCurrency();
                                                return money($total, $currencyCode, true)->format();
                                            })
                                            ->columnSpan(1),
                                    ]),
                            ])
                            ->addActionLabel('Add Line Item')
                            ->reorderable()
                            ->collapsible()
                            ->defaultItems(1),
                    ]),

                Forms\Components\Section::make('Totals')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Placeholder::make('subtotal_display')
                                    ->label('Subtotal')
                                    ->content(function (Forms\Get $get) {
                                        $lineItems = $get('lineItems') ?? [];
                                        $subtotal = collect($lineItems)->sum(function ($item) {
                                            return ((float) ($item['quantity'] ?? 0)) * ((float) ($item['unit_price'] ?? 0));
                                        });
                                        $currencyCode = $get('currency_code') ?? CurrencyAccessor::getDefaultCurrency();
                                        return money($subtotal, $currencyCode, true)->format();
                                    })
                                    ->live(),
                                Forms\Components\TextInput::make('tax_rate')
                                    ->label('Tax Rate (%)')
                                    ->numeric()
                                    ->step(0.01)
                                    ->default(0)
                                    ->live()
                                    ->suffix('%'),
                                Forms\Components\Placeholder::make('tax_total_display')
                                    ->label('Tax Total')
                                    ->content(function (Forms\Get $get) {
                                        $lineItems = $get('lineItems') ?? [];
                                        $subtotal = collect($lineItems)->sum(function ($item) {
                                            return ((float) ($item['quantity'] ?? 0)) * ((float) ($item['unit_price'] ?? 0));
                                        });
                                        $taxRate = (float) ($get('tax_rate') ?? 0);
                                        $taxTotal = $subtotal * ($taxRate / 100);
                                        $currencyCode = $get('currency_code') ?? CurrencyAccessor::getDefaultCurrency();
                                        return money($taxTotal, $currencyCode, true)->format();
                                    })
                                    ->live(),
                                Forms\Components\Placeholder::make('total_display')
                                    ->label('Total')
                                    ->content(function (Forms\Get $get) {
                                        $lineItems = $get('lineItems') ?? [];
                                        $subtotal = collect($lineItems)->sum(function ($item) {
                                            return ((float) ($item['quantity'] ?? 0)) * ((float) ($item['unit_price'] ?? 0));
                                        });
                                        $taxRate = (float) ($get('tax_rate') ?? 0);
                                        $taxTotal = $subtotal * ($taxRate / 100);
                                        $total = $subtotal + $taxTotal;
                                        $currencyCode = $get('currency_code') ?? CurrencyAccessor::getDefaultCurrency();
                                        return money($total, $currencyCode, true)->format();
                                    })
                                    ->live(),
                            ]),
                    ]),

                Forms\Components\Section::make('Additional Information')
                    ->schema([
                        Forms\Components\Textarea::make('notes')
                            ->label('Notes')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('expiration_date')
            ->columns([
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->searchable(),
                Tables\Columns\TextColumn::make('expiration_date')
                    ->label('Expiration Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('estimate_number')
                    ->label('Number')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('client.name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('total')
                    ->currency(static fn(SimpleEstimate $record) => $record->currency_code)
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options(EstimateStatus::class),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSimpleEstimates::route('/'),
            'create' => Pages\CreateSimpleEstimate::route('/create'),
            'view' => Pages\ViewSimpleEstimate::route('/{record}'),
            'edit' => Pages\EditSimpleEstimate::route('/{record}/edit'),
        ];
    }
}
