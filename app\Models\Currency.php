<?php

namespace App\Models;

use App\Traits\Blamable;
use App\Traits\CompanyOwned;
use Database\Factories\CurrencyFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Currency extends Model
{
    /** @use HasFactory<\Database\Factories\CurrencyFactory> */
    use HasFactory;

    use Blamable;
    use CompanyOwned;

    protected $table = 'currencies';

    protected $fillable = [
        'company_id',
        'name',
        'code',
        'precision',
        'symbol',
        'symbol_first',
        'decimal_mark',
        'thousands_separator',
        'enabled',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'enabled' => 'boolean',
        'symbol_first' => 'boolean',
    ];

    public function defaultCurrency(): HasOne
    {
        return $this->hasOne(CompanyDefault::class, 'currency_code', 'code');
    }

    public function isEnabled(): bool
    {
        return (bool) $this->enabled;
    }


    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class, 'currency_code', 'code');
    }

    public function estimates(): HasMany
    {
        return $this->hasMany(Estimate::class, 'currency_code', 'code');
    }

    public function deliveries(): HasMany
    {
        return $this->hasMany(Delivery::class, 'currency_code', 'code');
    }

    /* protected static function newFactory(): Factory
    {
        return CurrencyFactory::new();
    } */
}
