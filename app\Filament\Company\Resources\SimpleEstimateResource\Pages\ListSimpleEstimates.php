<?php

namespace App\Filament\Company\Resources\SimpleEstimateResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use App\Filament\Company\Resources\SimpleEstimateResource;

class ListSimpleEstimates extends ListRecords
{
    protected static string $resource = SimpleEstimateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
