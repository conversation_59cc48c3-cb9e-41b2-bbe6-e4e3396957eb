<?php

namespace App\Filament\Company\Resources\OfferingResource\Pages;

use Filament\Actions;
use App\Traits\HandlePageRedirect;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Company\Resources\OfferingResource;

class EditOffering extends EditRecord
{
    use HandlePageRedirect;

    protected static string $resource = OfferingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Convertir les attributs en champs sellable/purchasable
        if (isset($data['attributes'])) {
            $attributes = $data['attributes'] ?? [];
            $data['sellable'] = in_array('Sellable', $attributes);
            $data['purchasable'] = in_array('Purchasable', $attributes);

            // Supprimer le champ attributes car il n'existe pas dans la base de données
            unset($data['attributes']);
        }

        return $data;
    }
}
