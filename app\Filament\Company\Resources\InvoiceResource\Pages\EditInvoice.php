<?php

namespace App\Filament\Company\Resources\InvoiceResource\Pages;

use Filament\Actions;
use App\Enums\DocumentType;
use App\Traits\ManagesLineItems;

use App\Traits\HandlePageRedirect;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\Pages\EditRecord;
use App\View\Models\DocumentTotalViewModel;
use App\Filament\Company\Resources\InvoiceResource;

class EditInvoice extends EditRecord
{
    use HandlePageRedirect;
    use ManagesLineItems;

    protected static string $resource = InvoiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Assigner les numéros de ligne automatiquement
        if (isset($data['lineItems'])) {
            foreach ($data['lineItems'] as $index => &$lineItem) {
                $lineItem['line_number'] = $index + 1;
            }
        }

        return $data;
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        // Sauvegarder d'abord les données principales de la facture (sans les lineItems)
        $mainData = collect($data)->except(['lineItems'])->toArray();
        $record->update($mainData);

        // Gérer les lineItems (création, mise à jour et suppression)
        $this->handleLineItems($record, collect($data['lineItems'] ?? []));

        // Calculer et sauvegarder les totaux globaux en même temps
        $totals = $this->updateDocumentTotals($record, $data);
        $record->updateQuietly($totals);

        return $record;
    }

    protected function calculateAndSaveTotals(): void
    {
        $invoice = $this->record;

        // Récupérer les données du formulaire
        $formData = $this->form->getRawState();

        // Calculer les totaux avec DocumentTotalViewModel
        $viewModel = new DocumentTotalViewModel($formData, DocumentType::Invoice);
        $viewData = $viewModel->buildViewData();

        // Mettre à jour l'invoice avec les totaux calculés
        $invoice->update([
            'subtotal' => $this->convertToDatabase($viewData['subtotal'] ?? '0'),
            'tax_total' => $this->convertToDatabase($viewData['taxTotal'] ?? '0'),
            'discount_total' => $this->convertToDatabase($viewData['discountTotal'] ?? '0'),
            'total' => $this->convertToDatabase($viewData['grandTotal'] ?? '0'),
            'amount_due' => $this->convertToDatabase($viewData['amountDue'] ?? $viewData['grandTotal'] ?? '0'),
        ]);
    }

    protected function convertToDatabase(string $amount): float
    {
        if (empty($amount) || $amount === '0') {
            return 0.0;
        }

        // Conversion directe en float maintenant que nous utilisons des décimales
        return (float) str_replace(',', '', $amount);
    }
}
