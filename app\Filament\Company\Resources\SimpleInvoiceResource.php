<?php

namespace App\Filament\Company\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\Invoice;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Enums\DocumentType;
use App\Enums\OfferingType;
use App\Enums\InvoiceStatus;
use Filament\Resources\Resource;
use Awcodes\TableRepeater\Header;
use App\Utilities\CurrencyAccessor;
use Filament\Support\Enums\IconSize;
use Illuminate\Support\Facades\Auth;
use App\Enums\DocumentDiscountMethod;
use App\Filament\Forms\Components\ClientSelect;
use App\Filament\Forms\Components\CurrencySelect;
use App\Filament\Forms\Components\DocumentTotals;
use JaOcero\RadioDeck\Forms\Components\RadioDeck;
use App\Filament\Forms\Components\CustomTableRepeater;
use App\Filament\Company\Resources\SimpleInvoiceResource\Pages;

class SimpleInvoiceResource extends Resource
{
    protected static ?string $model = Invoice::class;

    protected static ?string $navigationLabel = 'Simple Invoices';

    protected static ?string $modelLabel = 'Simple Invoice';

    protected static ?string $pluralModelLabel = 'Simple Invoices';

    protected static ?string $navigationGroup = 'Simple Documents';

    public static function form(Form $form): Form
    {

        return $form
            ->schema([
                Forms\Components\Section::make('Invoice Information')
                    ->schema([
                        Forms\Components\Split::make([
                            Forms\Components\Group::make([
                                ClientSelect::make('client_id')
                                    ->label('Client')
                                    ->required(),
                                CurrencySelect::make('currency_code'),
                            ]),
                            Forms\Components\Group::make([
                                Forms\Components\TextInput::make('invoice_number')
                                    ->label('Invoice number')
                                    ->default(static fn() => Invoice::getNextDocumentNumber())
                                    ->required()
                                    ->maxLength(50)
                                    ->unique(Invoice::class, 'invoice_number', ignoreRecord: true)
                                    ->regex('/^[A-Za-z0-9\-_]+$/')
                                    ->validationMessages([
                                        'unique' => 'This invoice number already exists.',
                                        'regex' => 'Invoice number can only contain letters, numbers, hyphens, and underscores.',
                                    ]),
                                Forms\Components\DatePicker::make('date')
                                    ->label('Invoice date')
                                    ->live()
                                    ->default(now())
                                    ->required()
                                    ->maxDate(now()->addDays(30))
                                    ->minDate(now()->subYears(2)),
                                Forms\Components\Select::make('status')
                                    ->label('Status')
                                    ->options(InvoiceStatus::class)
                                    ->default(InvoiceStatus::Draft)
                                    ->required(),
                                Forms\Components\Select::make('discount_method')
                                    ->label('Discount method')
                                    ->options(DocumentDiscountMethod::class)
                                    ->required()
                                    ->default(DocumentDiscountMethod::PerDocument)
                                    ->afterStateUpdated(function ($state, Forms\Set $set) {
                                        $discountMethod = DocumentDiscountMethod::parse($state);
                                        if ($discountMethod?->isPerDocument()) {
                                            $set('lineItems.*.salesDiscounts', []);
                                        }
                                    })
                                    ->live(),
                            ])->grow(true),
                        ])->from('md'),
                    ]),

                Forms\Components\Section::make('Line Items')
                    ->schema([
                        CustomTableRepeater::make('lineItems')
                            ->hiddenLabel()
                            ->relationship('lineItems')
                            ->saveRelationshipsUsing(null)
                            ->dehydrated(true)
                            ->reorderable()
                            ->orderColumn('line_number')
                            ->reorderAtStart()
                            ->cloneable()
                            ->addActionLabel('Add an item')
                            ->headers(function (Forms\Get $get) {
                                $settings = Auth::user()?->currentCompany?->defaultInvoice;
                                $discountMethod = DocumentDiscountMethod::parse($get('discount_method'));
                                $hasDiscounts = $discountMethod?->isPerLineItem() ?? false;

                                $headers = [
                                    Header::make($settings?->resolveColumnLabel('offering_type', 'Type') ?? 'Type')->width('20%'),
                                    Header::make($settings?->resolveColumnLabel('item_name', 'Item') ?? 'Item')->width('30%'),
                                    Header::make($settings?->resolveColumnLabel('quantity_name', 'Quantity') ?? 'Quantity')->width('10%'),
                                    Header::make($settings?->resolveColumnLabel('unit_price', 'Price') ?? 'Price')->width('15%'),
                                ];

                                if ($hasDiscounts) {
                                    $headers[] = Header::make('Adjustments')->width('30%');
                                } else {
                                    $headers[] = Header::make('Taxes')->width('30%');
                                }

                                $headers[] = Header::make($settings?->resolveColumnLabel('amount_name', 'Amount') ?? 'Amount')
                                    ->width('10%')
                                    ->align('right');

                                return $headers;
                            })
                            ->schema([
                                Forms\Components\Group::make([
                                    RadioDeck::make('type')
                                        ->hiddenLabel()
                                        ->options(OfferingType::class)
                                        ->default(OfferingType::Product)
                                        ->icons(OfferingType::class)
                                        ->iconSize(IconSize::Small)
                                        ->color('primary')
                                        ->required(),
                                ])->columnSpan(1),
                                Forms\Components\Group::make([
                                    Forms\Components\TextInput::make('offering_name')
                                        ->label('Item')
                                        ->hiddenLabel()
                                        ->placeholder('Enter item name')
                                        ->required(),
                                    Forms\Components\Textarea::make('description')
                                        ->placeholder('Enter item description')
                                        ->hiddenLabel(),
                                ])->columnSpan(1),
                                Forms\Components\TextInput::make('quantity')
                                    ->required()
                                    ->numeric()
                                    ->live()
                                    ->minValue(0.01)
                                    ->maxValue(999999.99)
                                    ->step(0.01)
                                    ->default(1)
                                    ->validationMessages([
                                        'min' => 'Quantity must be greater than 0.',
                                        'max' => 'Quantity cannot exceed 999,999.99.',
                                        'numeric' => 'Quantity must be a valid number.',
                                    ]),
                                Forms\Components\TextInput::make('unit_price')
                                    ->hiddenLabel()
                                    ->required()
                                    ->numeric()
                                    ->step(0.01)
                                    ->live()
                                    ->minValue(0)
                                    ->maxValue(999999999.99)
                                    ->default(0)
                                    ->validationMessages([
                                        'min' => 'Unit price cannot be negative.',
                                        'max' => 'Unit price cannot exceed 999,999,999.99.',
                                        'numeric' => 'Unit price must be a valid number.',
                                    ]),
                                Forms\Components\Group::make([
                                    Forms\Components\TextInput::make('tax_rate')
                                        ->label('Taxes')
                                        ->hiddenLabel()
                                        ->placeholder('Enter tax rate')
                                        ->suffix('%')
                                        ->prefix('Tax:')
                                        ->numeric()
                                        ->step(0.01)
                                        ->minValue(0)
                                        ->maxValue(100)
                                        ->live()
                                        ->default(0),
                                    Forms\Components\TextInput::make('discount_rate')
                                        ->label('Discounts')
                                        ->hiddenLabel()
                                        ->placeholder('Enter discount rate')
                                        ->suffix('%')
                                        ->prefix('Disc:')
                                        ->numeric()
                                        ->step(0.01)
                                        ->minValue(0)
                                        ->maxValue(100)
                                        ->live()
                                        ->default(0)
                                        ->visible(function (Forms\Get $get) {
                                            $discountMethod = DocumentDiscountMethod::parse($get('../../discount_method'));
                                            return $discountMethod?->isPerLineItem() ?? false;
                                        }),
                                ])->columnSpan(1),

                                Forms\Components\Placeholder::make('line_total')
                                    ->hiddenLabel()
                                    ->extraAttributes(['class' => 'text-right font-semibold'])
                                    ->content(function (Forms\Get $get) {
                                        $quantity = (float) ($get('quantity') ?? 0);
                                        $unitPrice = (float) ($get('unit_price') ?? 0);
                                        $taxRate = (float) ($get('tax_rate') ?? 0);
                                        $discountRate = (float) ($get('discount_rate') ?? 0);

                                        // Calcul du sous-total
                                        $subtotal = $quantity * $unitPrice;

                                        // Calcul des taxes
                                        $taxAmount = $subtotal * ($taxRate / 100);

                                        // Calcul des remises (seulement si per line item)
                                        $discountAmount = 0;
                                        $discountMethod = DocumentDiscountMethod::parse($get('../../discount_method'));
                                        if ($discountMethod?->isPerLineItem()) {
                                            $discountAmount = $subtotal * ($discountRate / 100);
                                        }

                                        // Total final
                                        $total = $subtotal + $taxAmount - $discountAmount;

                                        $currencyCode = CurrencyAccessor::getDefaultCurrency();
                                        return money($total, $currencyCode, true)->format();
                                    })
                                    ->live(),
                            ]),

                        DocumentTotals::make()
                            ->type(DocumentType::Invoice),
                    ]),

                Forms\Components\Section::make('Additional Information')
                    ->schema([
                        Forms\Components\Textarea::make('notes')
                            ->label('Notes')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->searchable(),
                Tables\Columns\TextColumn::make('date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('invoice_number')
                    ->label('Number')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('client.name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('total')
                    ->currency(static fn(Invoice $record) => $record->currency_code)
                    ->sortable(),
                Tables\Columns\TextColumn::make('amount_due')
                    ->label('Amount Due')
                    ->currency(static fn(Invoice $record) => $record->currency_code)
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options(InvoiceStatus::class),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSimpleInvoices::route('/'),
            'create' => Pages\CreateSimpleInvoice::route('/create'),
            'view' => Pages\ViewSimpleInvoice::route('/{record}'),
            'edit' => Pages\EditSimpleInvoice::route('/{record}/edit'),
        ];
    }
}
