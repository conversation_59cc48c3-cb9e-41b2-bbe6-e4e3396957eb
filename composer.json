{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "ext-intl": "*", "akaunting/laravel-money": "^6.0", "andrewdwallo/filament-companies": "^4.2", "andrewdwallo/filament-selectify": "^2.0", "awcodes/filament-table-repeater": "^3.1", "barryvdh/laravel-snappy": "^1.0", "bezhansalleh/filament-language-switch": "^3.1", "bezhansalleh/filament-panel-switch": "^1.1", "bezhansalleh/filament-shield": "^3.3", "codewithdennis/filament-simple-alert": "^3.0", "filament/filament": "^3.3", "guava/filament-clusters": "^1.5", "guzzlehttp/guzzle": "^7.9", "jaocero/radio-deck": "^1.2", "lara-zeus/qr": "^2.0", "laravel/framework": "^11.31", "laravel/prompts": "^0.3.5", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "leandrocfe/filament-apex-charts": "^3.2", "livewire/livewire": "^3.6", "malzariey/filament-daterangepicker-filter": "^4.0", "marcelweidum/filament-expiration-notice": "^1.0", "outerweb/filament-translatable-fields": "^2.1", "squirephp/model": "^3.10", "squirephp/repository": "^3.10", "symfony/intl": "^7.3"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pail": "^1.1", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.0.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}