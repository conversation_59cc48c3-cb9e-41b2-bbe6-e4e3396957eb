<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\Client;
use App\Models\Invoice;
use App\Models\Offering;
use App\Models\DocumentLineItem;
use App\Models\Adjustment;
use App\Enums\AdjustmentType;
use App\Enums\AdjustmentCategory;
use App\Enums\InvoiceStatus;
use Illuminate\Database\Seeder;

class InvoiceSeeder extends Seeder
{
    public function run(): void
    {
        $companies = Company::all();

        foreach ($companies as $company) {
            $clients = Client::where('company_id', $company->id)->get();

            if ($clients->isNotEmpty()) {
                foreach ($clients->take(3) as $client) {
                    $invoice = $this->createInvoiceForClient($company, $client);

                    // Créer les lignes de document polymorphiques
                    $this->createLineItemsForInvoice($invoice);
                }
            }
        }

        $this->command->info('Factures créées avec succès !');
    }

    /**
     * Créer une facture pour un client
     */
    private function createInvoiceForClient(Company $company, Client $client): Invoice
    {
        return Invoice::create([
            'company_id' => $company->id,
            'client_id' => $client->id,
            'invoice_number' => 'FAC-' . fake()->unique()->numerify('####'),
            'date' => fake()->dateTimeBetween('-3 months', 'now'),
            'due_date' => fake()->dateTimeBetween('now', '+1 month'),
            'status' => InvoiceStatus::Draft,
            'subtotal' => 0, // Sera calculé après les lignes
            'tax_total' => 0,
            'discount_total' => 0,
            'total' => 0,
            'notes' => fake()->optional()->sentence(),
            'created_by' => $company->user_id,
            'updated_by' => $company->user_id,
        ]);
    }

    /**
     * Créer des lignes de document polymorphiques pour une facture
     */
    private function createLineItemsForInvoice(Invoice $invoice): void
    {
        $offerings = Offering::where('company_id', $invoice->company_id)
            ->where('sellable', true)
            ->take(3)
            ->get();

        if ($offerings->isEmpty()) {
            // Créer des lignes sans offering si aucun produit disponible
            $this->createGenericLineItems($invoice);
            return;
        }

        foreach ($offerings as $index => $offering) {
            $lineItem = $invoice->lineItems()->create([
                'company_id' => $invoice->company_id,
                'offering_id' => $offering->id,
                'description' => $offering->description ?? $offering->name,
                'quantity' => fake()->numberBetween(1, 5),
                'unit_price' => $offering->price,
                'tax_total' => 0,
                'discount_total' => 0,
                'line_number' => $index + 1,
                'created_by' => $invoice->created_by,
                'updated_by' => $invoice->updated_by,
            ]);

            // Attacher les ajustements polymorphiques à la ligne
            $this->attachAdjustmentsToLineItem($lineItem);
        }
    }

    /**
     * Créer des lignes génériques si aucun produit n'est disponible
     */
    private function createGenericLineItems(Invoice $invoice): void
    {
        $genericItems = [
            ['name' => 'Consultation', 'price' => 75.00],
            ['name' => 'Développement', 'price' => 120.00],
            ['name' => 'Formation', 'price' => 90.00],
        ];

        foreach ($genericItems as $index => $item) {
            $lineItem = $invoice->lineItems()->create([
                'company_id' => $invoice->company_id,
                'offering_id' => null,
                'description' => $item['name'],
                'quantity' => fake()->numberBetween(1, 3),
                'unit_price' => $item['price'],
                'tax_total' => 0,
                'discount_total' => 0,
                'line_number' => $index + 1,
                'created_by' => $invoice->created_by,
                'updated_by' => $invoice->updated_by,
            ]);

            // Attacher les ajustements polymorphiques à la ligne
            $this->attachAdjustmentsToLineItem($lineItem);
        }
    }

    /**
     * Attacher des ajustements polymorphiques à une ligne de document
     */
    private function attachAdjustmentsToLineItem(DocumentLineItem $lineItem): void
    {
        $adjustments = Adjustment::where('company_id', $lineItem->company_id)->get();

        if ($adjustments->isEmpty()) {
            return;
        }

        // Attacher des taxes de vente (toujours pour les factures)
        $salesTaxes = $adjustments->where('type', AdjustmentType::Sales)
            ->where('category', AdjustmentCategory::Tax)
            ->take(1);

        foreach ($salesTaxes as $tax) {
            $lineItem->adjustments()->attach($tax->id);
        }

        // Attacher des remises de vente (optionnel, 30% de chance)
        if (fake()->boolean(30)) {
            $salesDiscounts = $adjustments->where('type', AdjustmentType::Sales)
                ->where('category', AdjustmentCategory::Discount)
                ->take(1);

            foreach ($salesDiscounts as $discount) {
                $lineItem->adjustments()->attach($discount->id);
            }
        }
    }
}
