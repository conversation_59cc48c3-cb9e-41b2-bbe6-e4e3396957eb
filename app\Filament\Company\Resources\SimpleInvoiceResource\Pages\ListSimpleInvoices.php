<?php

namespace App\Filament\Company\Resources\SimpleInvoiceResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use App\Filament\Company\Resources\SimpleInvoiceResource;

class ListSimpleInvoices extends ListRecords
{
    protected static string $resource = SimpleInvoiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
