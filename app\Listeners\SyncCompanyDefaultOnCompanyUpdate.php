<?php

namespace App\Listeners;

use App\Models\Company;
use App\Services\CompanyDefaultSyncService;
use Illuminate\Support\Facades\Log;
use Wallo\FilamentCompanies\Events\CompanyUpdated;

class SyncCompanyDefaultOnCompanyUpdate
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     * 
     * Synchronise CompanyDefault lorsqu'une entreprise est mise à jour
     */
    public function handle(CompanyUpdated $event): void
    {
        try {
            /** @var Company $company */
            $company = $event->company;
            
            Log::info("Synchronisation CompanyDefault déclenchée par mise à jour de l'entreprise: {$company->name} (ID: {$company->id})");
            
            $syncService = app(CompanyDefaultSyncService::class);
            $syncService->syncFromCompany($company);
            
            Log::info("Synchronisation CompanyDefault terminée pour l'entreprise {$company->id}");
            
        } catch (\Exception $e) {
            Log::error("Erreur lors de la synchronisation CompanyDefault pour l'entreprise {$event->company->name}: " . $e->getMessage());
        }
    }
}
