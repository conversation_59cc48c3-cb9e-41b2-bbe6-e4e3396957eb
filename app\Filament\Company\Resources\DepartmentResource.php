<?php

namespace App\Filament\Company\Resources;

use App\Filament\Company\Resources\DepartmentResource\Pages;
use App\Filament\Company\Resources\DepartmentResource\RelationManagers\ChildrenRelationManager;
use App\Filament\Company\Resources\DepartmentResource\RelationManagers\EmployeeshipsRelationManager;
use App\Models\Department;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class DepartmentResource extends Resource
{
    protected static ?string $model = Department::class;

    //protected static ?string $navigationIcon = 'heroicon-o-building-office-2';

    protected static ?string $navigationGroup = 'Human Resources';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->placeholder('Department name')
                    ->maxLength(255)
                    ->columnSpanFull(),

                Forms\Components\Select::make('manager_id')
                    ->relationship(
                        name: 'manager',
                        titleAttribute: 'name',
                        modifyQueryUsing: static function (Builder $query) {
                            $company = auth()->user()->currentCompany;
                            if (!$company) {
                                return $query->whereRaw('1 = 0');
                            }
                            $companyUsers = $company->allUsers()->pluck('id')->toArray();
                            return $query->whereIn('id', $companyUsers);
                        }
                    )
                    ->placeholder('Select department manager')
                    ->searchable()
                    ->preload()
                    ->nullable(),

                Forms\Components\Select::make('parent_id')
                    ->relationship('parent', 'name')
                    ->placeholder('Select parent department')
                    ->searchable()
                    ->preload()
                    ->nullable(),

                Forms\Components\Textarea::make('description')
                    ->placeholder('Department description')
                    ->rows(3)
                    ->columnSpanFull(),

                Forms\Components\Placeholder::make('employees_count')
                    ->label('Total Employees')
                    ->content(fn(?Department $record): string => $record ? $record->employeeships()->count() . ' employees' : '0 employees')
                    ->hiddenOn('create'),

                Forms\Components\Placeholder::make('subdepartments_count')
                    ->label('Sub-departments')
                    ->content(fn(?Department $record): string => $record ? $record->children()->count() . ' sub-departments' : '0 sub-departments')
                    ->hiddenOn('create'),
            ])
            ->columns(2);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Department Name')
                    ->weight('semibold')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('manager.name')
                    ->label('Manager')
                    ->searchable()
                    ->sortable()
                    ->placeholder('No manager assigned'),

                Tables\Columns\TextColumn::make('parent.name')
                    ->label('Parent Department')
                    ->searchable()
                    ->sortable()
                    ->placeholder('Root department'),

                Tables\Columns\TextColumn::make('employeeships_count')
                    ->label('Employees')
                    ->badge()
                    ->counts('employeeships')
                    ->color('success'),

                Tables\Columns\TextColumn::make('children_count')
                    ->label('Sub-departments')
                    ->badge()
                    ->counts('children')
                    ->color('info'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Updated')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('manager_id')
                    ->relationship('manager', 'name')
                    ->label('Manager')
                    ->placeholder('All managers'),

                Tables\Filters\SelectFilter::make('parent_id')
                    ->relationship('parent', 'name')
                    ->label('Parent Department')
                    ->placeholder('All departments'),

                Tables\Filters\Filter::make('has_employees')
                    ->label('Has Employees')
                    ->query(fn(Builder $query): Builder => $query->has('employeeships')),

                Tables\Filters\Filter::make('has_subdepartments')
                    ->label('Has Sub-departments')
                    ->query(fn(Builder $query): Builder => $query->has('children')),
            ])
            ->filtersTriggerAction(
                fn(Tables\Actions\Action $action) => $action
                    ->button()
                    ->label('Filters')
                    ->icon('heroicon-m-funnel')
                    ->slideOver()
            )
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('name', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            EmployeeshipsRelationManager::class,
            ChildrenRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDepartments::route('/'),
            'create' => Pages\CreateDepartment::route('/create'),
            'view' => Pages\ViewDepartment::route('/{record}'),
            'edit' => Pages\EditDepartment::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['name', 'description', 'manager.name', 'parent.name'];
    }
}
