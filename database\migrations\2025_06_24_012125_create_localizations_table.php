<?php
use Illuminate\Database\Migrations\Migration;

use Illuminate\Database\Schema\Blueprint;

use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('localizations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained()->cascadeOnDelete();
            $table->string('language')->default('fr');
            $table->string('timezone')->nullable();
            $table->string('date_format');
            $table->string('time_format');
            $table->unsignedTinyInteger('fiscal_year_end_month');
            $table->unsignedTinyInteger('fiscal_year_end_day');
            $table->unsignedTinyInteger('week_start');
            $table->string('number_format');
            $table->boolean('percent_first')->default(false);
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('localizations');
    }
};
