<?php

namespace App\Filament\Company\Resources\SimpleEstimateResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Company\Resources\SimpleEstimateResource;

class EditSimpleEstimate extends EditRecord
{
    protected static string $resource = SimpleEstimateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Convert bigInteger values to decimal for form display
        $data['subtotal'] = $data['subtotal'] / 100;
        $data['tax_total'] = $data['tax_total'] / 100;
        $data['discount_total'] = $data['discount_total'] / 100;
        $data['total'] = $data['total'] / 100;

        // Convert line items from cents to decimal
        if (isset($data['lineItems'])) {
            foreach ($data['lineItems'] as &$lineItem) {
                $lineItem['quantity'] = $lineItem['quantity'] / 100;
                $lineItem['unit_price'] = $lineItem['unit_price'] / 100;
                $lineItem['line_total'] = $lineItem['line_total'] / 100;
            }
        }

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Calculate totals based on line items with manual taxes and discounts
        $lineItems = $data['lineItems'] ?? [];

        $subtotal = collect($lineItems)->sum(function ($item) {
            $quantity = (float) ($item['quantity'] ?? 0);
            $unitPrice = (float) ($item['unit_price'] ?? 0);
            return $quantity * $unitPrice;
        });

        $taxTotal = collect($lineItems)->sum(function ($item) {
            $quantity = (float) ($item['quantity'] ?? 0);
            $unitPrice = (float) ($item['unit_price'] ?? 0);
            $lineTotal = $quantity * $unitPrice;

            if (!empty($item['taxes'])) {
                $taxValue = (float) $item['taxes'];
                return $taxValue <= 100 ? $lineTotal * ($taxValue / 100) : $taxValue;
            }
            return 0;
        });

        $discountTotal = collect($lineItems)->sum(function ($item) {
            $quantity = (float) ($item['quantity'] ?? 0);
            $unitPrice = (float) ($item['unit_price'] ?? 0);
            $lineTotal = $quantity * $unitPrice;

            if (!empty($item['discounts'])) {
                $discountValue = (float) $item['discounts'];
                return $discountValue <= 100 ? $lineTotal * ($discountValue / 100) : $discountValue;
            }
            return 0;
        });

        $total = $subtotal + $taxTotal - $discountTotal;

        // Convert to cents for storage (bigInteger)
        $data['subtotal'] = (int) ($subtotal * 100);
        $data['tax_total'] = (int) ($taxTotal * 100);
        $data['discount_total'] = (int) ($discountTotal * 100);
        $data['total'] = (int) ($total * 100);

        return $data;
    }

    protected function afterSave(): void
    {
        // Update line items with calculated totals
        $record = $this->record;

        foreach ($record->lineItems as $index => $lineItem) {
            $quantity = $lineItem->quantity;
            $unitPrice = $lineItem->unit_price;
            $subtotal = $quantity * $unitPrice;

            $lineItem->update([
                'subtotal' => $subtotal,
                'total' => $subtotal, // No taxes/discounts per line in simple version
                'tax_total' => 0,
                'discount_total' => 0,
            ]);
        }
    }
}
