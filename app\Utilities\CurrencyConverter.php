<?php

namespace App\Utilities;

use App\Utilities\CurrencyAccessor;

class CurrencyConverter
{






    public static function convertToFloat(string|float $amount, ?string $currency = null): float
    {
        $currency ??= CurrencyAccessor::getDefaultCurrency();

        return money($amount, $currency, true)->getValue();
    }

    public static function formatToMoney(string|float|int $amount, ?string $currency = null): string
    {
        $currency ??= CurrencyAccessor::getDefaultCurrency();

        return money($amount, $currency, true)->format();
    }

    public static function isValidAmount(?string $amount, ?string $currency = null): bool
    {
        $currency ??= CurrencyAccessor::getDefaultCurrency();

        if (blank($amount)) {
            return false;
        }

        try {
            money($amount, $currency);
        } catch (\Exception $e) {
            return false;
        }

        return true;
    }
}
