<?php

namespace App\Filament\Company\Resources;

use App\Filament\Company\Resources\EmployeeshipResource\Pages;
use App\Filament\Company\Resources\EmployeeshipResource\RelationManagers;
use App\Models\Employeeship;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class EmployeeshipResource extends Resource
{
    protected static ?string $model = Employeeship::class;

    //protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'Human Resources';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Tabs::make('Employee Information')
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('General Information')
                            ->schema([
                                Forms\Components\Select::make('user_id')
                                    ->relationship('user', 'name')
                                    ->required()
                                    ->searchable()
                                    ->preload(),

                                Forms\Components\Select::make('contact_id')
                                    ->relationship('contact', 'first_name')
                                    ->searchable()
                                    ->preload(),

                                Forms\Components\Select::make('department_id')
                                    ->relationship('department', 'name')
                                    ->searchable()
                                    ->preload(),

                                Forms\Components\TextInput::make('job_title')
                                    ->maxLength(255),

                                Forms\Components\Select::make('role')
                                    ->options([
                                        'employee' => 'Employee',
                                        'manager' => 'Manager',
                                        'admin' => 'Admin',
                                        'hr' => 'HR',
                                    ]),

                                Forms\Components\Select::make('employment_type')
                                    ->options([
                                        'full_time' => 'Full Time',
                                        'part_time' => 'Part Time',
                                        'contract' => 'Contract',
                                        'intern' => 'Intern',
                                        'temporary' => 'Temporary',
                                    ]),

                                Forms\Components\DatePicker::make('hire_date')
                                    ->native(false),

                                Forms\Components\DatePicker::make('start_date')
                                    ->native(false),

                                Forms\Components\FileUpload::make('photo')
                                    ->image()
                                    ->directory('employee-photos')
                                    ->columnSpanFull(),
                            ])
                            ->columns(2),

                        Forms\Components\Tabs\Tab::make('Personal Information')
                            ->schema([
                                Forms\Components\DatePicker::make('date_of_birth')
                                    ->native(false),

                                Forms\Components\Select::make('gender')
                                    ->options([
                                        'male' => 'Male',
                                        'female' => 'Female',
                                        'other' => 'Other',
                                        'prefer_not_to_say' => 'Prefer not to say',
                                    ]),

                                Forms\Components\Select::make('marital_status')
                                    ->options([
                                        'single' => 'Single',
                                        'married' => 'Married',
                                        'divorced' => 'Divorced',
                                        'widowed' => 'Widowed',
                                        'separated' => 'Separated',
                                    ]),

                                Forms\Components\TextInput::make('nationality')
                                    ->maxLength(255),
                            ])
                            ->columns(2),

                        Forms\Components\Tabs\Tab::make('Compensation')
                            ->schema([
                                Forms\Components\TextInput::make('compensation_amount')
                                    ->numeric()
                                    ->prefix('$'),

                                Forms\Components\Select::make('compensation_type')
                                    ->options([
                                        'salary' => 'Salary',
                                        'hourly' => 'Hourly',
                                        'commission' => 'Commission',
                                        'contract' => 'Contract',
                                    ]),

                                Forms\Components\Select::make('compensation_frequency')
                                    ->options([
                                        'weekly' => 'Weekly',
                                        'bi_weekly' => 'Bi-weekly',
                                        'monthly' => 'Monthly',
                                        'quarterly' => 'Quarterly',
                                        'annually' => 'Annually',
                                    ]),

                                Forms\Components\TextInput::make('bank_account_number')
                                    ->maxLength(255),
                            ])
                            ->columns(2),

                        Forms\Components\Tabs\Tab::make('Education')
                            ->schema([
                                Forms\Components\Select::make('education_level')
                                    ->options([
                                        'high_school' => 'High School',
                                        'associate' => 'Associate Degree',
                                        'bachelor' => 'Bachelor Degree',
                                        'master' => 'Master Degree',
                                        'doctorate' => 'Doctorate',
                                        'other' => 'Other',
                                    ]),

                                Forms\Components\TextInput::make('field_of_study')
                                    ->maxLength(255),

                                Forms\Components\TextInput::make('school_name')
                                    ->maxLength(255)
                                    ->columnSpanFull(),
                            ])
                            ->columns(2),

                        Forms\Components\Tabs\Tab::make('Emergency Contact')
                            ->schema([
                                Forms\Components\TextInput::make('emergency_contact_name')
                                    ->maxLength(255),

                                Forms\Components\TextInput::make('emergency_contact_phone_number')
                                    ->tel()
                                    ->maxLength(255),

                                Forms\Components\TextInput::make('emergency_contact_email')
                                    ->email()
                                    ->maxLength(255)
                                    ->columnSpanFull(),
                            ])
                            ->columns(2),
                    ])
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('photo')
                    ->circular()
                    ->size(40),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('Employee Name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('job_title')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('department.name')
                    ->label('Department')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('role')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'admin' => 'danger',
                        'manager' => 'warning',
                        'hr' => 'info',
                        'employee' => 'success',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('employment_type')
                    ->label('Type')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'full_time' => 'success',
                        'part_time' => 'warning',
                        'contract' => 'info',
                        'intern' => 'gray',
                        'temporary' => 'danger',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('compensation_amount')
                    ->label('Compensation')
                    ->money()
                    ->sortable(),

                Tables\Columns\TextColumn::make('hire_date')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('start_date')
                    ->date()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('department_id')
                    ->relationship('department', 'name')
                    ->label('Department'),

                Tables\Filters\SelectFilter::make('role')
                    ->options([
                        'employee' => 'Employee',
                        'manager' => 'Manager',
                        'admin' => 'Admin',
                        'hr' => 'HR',
                    ]),

                Tables\Filters\SelectFilter::make('employment_type')
                    ->options([
                        'full_time' => 'Full Time',
                        'part_time' => 'Part Time',
                        'contract' => 'Contract',
                        'intern' => 'Intern',
                        'temporary' => 'Temporary',
                    ]),
            ], layout: Tables\Enums\FiltersLayout::AboveContentCollapsible)
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEmployeeships::route('/'),
            'create' => Pages\CreateEmployeeship::route('/create'),
            'view' => Pages\ViewEmployeeship::route('/{record}'),
            'edit' => Pages\EditEmployeeship::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['user.name', 'job_title', 'department.name'];
    }
}
