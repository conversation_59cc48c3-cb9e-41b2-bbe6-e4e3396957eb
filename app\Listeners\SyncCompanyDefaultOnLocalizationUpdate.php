<?php

namespace App\Listeners;

use App\Models\Localization;
use App\Services\CompanyDefaultSyncService;
use Illuminate\Support\Facades\Log;

class SyncCompanyDefaultOnLocalizationUpdate
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event when Localization is created.
     */
    public function created(Localization $localization): void
    {
        $this->syncCompanyDefault($localization, 'created');
    }

    /**
     * Handle the event when Localization is updated.
     */
    public function updated(Localization $localization): void
    {
        $this->syncCompanyDefault($localization, 'updated');
    }

    /**
     * Synchronise CompanyDefault lorsqu'une localisation est modifiée
     */
    private function syncCompanyDefault(Localization $localization, string $action): void
    {
        try {
            Log::info("Synchronisation CompanyDefault déclenchée par {$action} de la localisation (ID: {$localization->id}, Company: {$localization->company_id})");
            
            $syncService = app(CompanyDefaultSyncService::class);
            $syncService->syncFromLocalization($localization);
            
            Log::info("Synchronisation CompanyDefault terminée pour la localisation {$localization->id}");
            
        } catch (\Exception $e) {
            Log::error("Erreur lors de la synchronisation CompanyDefault pour la localisation {$localization->id}: " . $e->getMessage());
        }
    }
}
