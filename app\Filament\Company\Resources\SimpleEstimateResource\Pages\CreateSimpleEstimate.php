<?php

namespace App\Filament\Company\Resources\SimpleEstimateResource\Pages;

use Filament\Resources\Pages\CreateRecord;
use App\Filament\Company\Resources\SimpleEstimateResource;

class CreateSimpleEstimate extends CreateRecord
{
    protected static string $resource = SimpleEstimateResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Calculate raw totals for database storage
        $lineItems = $data['lineItems'] ?? [];

        $subtotal = collect($lineItems)->sum(function ($item) {
            $quantity = (int) (((float) ($item['quantity'] ?? 0)) * 100); // Convert to cents
            $unitPrice = (int) (((float) ($item['unit_price'] ?? 0)) * 100); // Convert to cents
            return ($quantity * $unitPrice) / 100; // Calculate in cents then convert back
        });

        $taxTotal = collect($lineItems)->sum(function ($item) {
            $quantity = (int) (((float) ($item['quantity'] ?? 0)) * 100);
            $unitPrice = (int) (((float) ($item['unit_price'] ?? 0)) * 100);
            $lineTotal = ($quantity * $unitPrice) / 100;

            if (!empty($item['taxes'])) {
                $taxValue = (float) $item['taxes'];
                return $taxValue <= 100 ? $lineTotal * ($taxValue / 100) : $taxValue;
            }
            return 0;
        });

        $discountTotal = collect($lineItems)->sum(function ($item) {
            $quantity = (int) (((float) ($item['quantity'] ?? 0)) * 100);
            $unitPrice = (int) (((float) ($item['unit_price'] ?? 0)) * 100);
            $lineTotal = ($quantity * $unitPrice) / 100;

            if (!empty($item['discounts'])) {
                $discountValue = (float) $item['discounts'];
                return $discountValue <= 100 ? $lineTotal * ($discountValue / 100) : $discountValue;
            }
            return 0;
        });

        $total = $subtotal + $taxTotal - $discountTotal;

        // Convert to cents for storage (bigInteger)
        $data['subtotal'] = (int) ($subtotal * 100);
        $data['tax_total'] = (int) ($taxTotal * 100);
        $data['discount_total'] = (int) ($discountTotal * 100);
        $data['total'] = (int) ($total * 100);

        return $data;
    }

    protected function afterCreate(): void
    {
        // Update line items with calculated totals
        $record = $this->record;

        foreach ($record->lineItems as $lineItem) {
            // Convert from cents back to decimal for calculation
            $quantity = $lineItem->quantity / 100;
            $unitPrice = $lineItem->unit_price / 100;
            $lineTotal = $quantity * $unitPrice;

            $lineItem->update([
                'line_total' => (int) ($lineTotal * 100), // Convert back to cents
            ]);
        }
    }
}
