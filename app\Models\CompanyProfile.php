<?php

namespace App\Models;

use App\Enums\EntityType;
use App\Traits\Blamable;
use App\Traits\CompanyOwned;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Database\Factories\CompanyProfileFactory;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CompanyProfile extends Model
{
    /** @use HasFactory<\Database\Factories\CompanyProfileFactory> */
    use Blamable;
    use CompanyOwned;
    use HasFactory;

    protected $table = 'company_profiles';

    protected $fillable = [
        'company_id',
        'entity_type',
        'logo',
        'email',
        'rccm',
        'fiscal_number',
        'siret_number',
        'currency_code',
        'tax_id',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'entity_type' => EntityType::class,
    ];

    protected $appends = [
        'logo_url',
    ];

    /**
     * Événements du modèle pour synchroniser CompanyDefault
     */
    protected static function booted(): void
    {
        static::created(function (CompanyProfile $profile) {
            app(\App\Listeners\SyncCompanyDefaultOnProfileUpdate::class)->created($profile);
        });

        static::updated(function (CompanyProfile $profile) {
            app(\App\Listeners\SyncCompanyDefaultOnProfileUpdate::class)->updated($profile);
        });
    }

    protected function logoUrl(): Attribute
    {
        return Attribute::get(static function (mixed $value, array $attributes): ?string {
            if (array_key_exists('logo', $attributes) && $attributes['logo']) {
                return Storage::disk('public')->url($attributes['logo']);
            }

            return null;
        });

    }

    public function address(): MorphOne
    {
        return $this->morphOne(Address::class, 'addressable');
    }

    protected static function newFactory(): Factory
    {
        return CompanyProfileFactory::new();
    }
}
