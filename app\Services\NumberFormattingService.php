<?php

namespace App\Services;

use App\Models\Localization;
use App\Enums\NumberFormat;
use Illuminate\Support\Facades\Auth;

class NumberFormattingService
{
    /**
     * Get the company's number format
     */
    public static function getCompanyNumberFormat(): NumberFormat
    {
        $companyId = Auth::user()?->current_company_id;
        
        if (!$companyId) {
            return NumberFormat::CommaDot; // Default fallback
        }

        $localization = Localization::where('company_id', $companyId)->first();
        
        return $localization?->number_format ?? NumberFormat::CommaDot;
    }

    /**
     * Get formatting parameters for the company
     */
    public static function getFormattingParameters(): array
    {
        $numberFormat = self::getCompanyNumberFormat();
        
        return [
            $numberFormat->getDecimalMark(),
            $numberFormat->getThousandsSeparator()
        ];
    }

    /**
     * Format a number according to company localization
     */
    public static function formatNumber(float $number, int $decimals = 2): string
    {
        [$decimalMark, $thousandsSeparator] = self::getFormattingParameters();
        
        return number_format($number, $decimals, $decimalMark, $thousandsSeparator);
    }

    /**
     * Parse a localized number string to float
     */
    public static function parseNumber(string $value): float
    {
        [$decimalMark, $thousandsSeparator] = self::getFormattingParameters();
        
        // Remove thousands separators and replace decimal mark with dot
        $normalized = str_replace([$thousandsSeparator, $decimalMark], ['', '.'], $value);
        
        return (float) $normalized;
    }

    /**
     * Format a monetary amount with currency
     */
    public static function formatMoney(float $amount, string $currencyCode, int $decimals = 2): string
    {
        $formattedNumber = self::formatNumber($amount, $decimals);
        
        return $formattedNumber . ' ' . $currencyCode;
    }

    /**
     * Convert bigInteger (cents) to formatted decimal string
     */
    public static function formatFromCents(int $cents, int $decimals = 2): string
    {
        $amount = $cents / 100;
        return self::formatNumber($amount, $decimals);
    }

    /**
     * Convert formatted decimal string to bigInteger (cents)
     */
    public static function parseToCents(string $value): int
    {
        $amount = self::parseNumber($value);
        return (int) round($amount * 100);
    }

    /**
     * Format percentage according to company localization
     */
    public static function formatPercentage(float $percentage, int $decimals = 2): string
    {
        $companyId = Auth::user()?->current_company_id;
        $localization = Localization::where('company_id', $companyId)->first();
        
        $formattedNumber = self::formatNumber($percentage, $decimals);
        
        if ($localization?->percent_first) {
            return '%' . $formattedNumber;
        }
        
        return $formattedNumber . '%';
    }
}
