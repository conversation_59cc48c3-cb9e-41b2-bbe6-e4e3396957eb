<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum PaymentChannel: string implements HasLabel
{
    case Online = 'online';
    case InStore = 'in_store';
    case Other = 'other';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Online => 'Online',
            self::InStore => 'In Store',
            self::Other => 'Other',
        };
    }
}

