<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;
use Filament\Support\Colors\Color;
use JaOcero\RadioDeck\Contracts\HasIcons;

enum ClientType: string implements HasColor, HasIcon, HasLabel, HasIcons
{
    case Individual = 'individual';
    case Business = 'business';
    case Government = 'government';
    case Nonprofit = 'nonprofit';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Individual => 'Individual',
            self::Business => 'Business',
            self::Government => 'Government',
            self::Nonprofit => 'Nonprofit',
        };
    }

    public function getColor(): string|array|null
    {
        return match ($this) {
            self::Individual => Color::Blue,
            self::Business => Color::Green,
            self::Government => Color::Purple,
            self::Nonprofit => Color::Orange,
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::Individual => 'heroicon-o-user',
            self::Business => 'heroicon-o-building-office',
            self::Government => 'heroicon-o-building-library',
            self::Nonprofit => 'heroicon-o-heart',
        };
    }

    public function getIcons(): ?string
    {
        return match ($this) {
            self::Individual => 'heroicon-o-user',
            self::Business => 'heroicon-o-building-office',
            self::Government => 'heroicon-o-building-library',
            self::Nonprofit => 'heroicon-o-heart',
        };
    }
}
