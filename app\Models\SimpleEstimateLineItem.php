<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SimpleEstimateLineItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'simple_estimate_id',
        'line_number',
        'offering_name',
        'description',
        'quantity',
        'unit_price',
        'taxes',
        'discounts',
        'line_total',
    ];

    protected $casts = [
        'line_number' => 'integer',
        'quantity' => 'integer',
        'unit_price' => 'integer',
        'line_total' => 'integer',
    ];

    public function simpleEstimate(): BelongsTo
    {
        return $this->belongsTo(SimpleEstimate::class);
    }
}
