<?php

namespace Database\Factories;

use App\Models\Client;
use App\Models\Company;
use App\Models\User;
use App\Enums\ClientType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Client>
 */
class ClientFactory extends Factory
{
    protected $model = Client::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $type = $this->faker->randomElement(ClientType::cases());

        return [
            'company_id' => null, // Sera fourni par le seeder
            'name' => $type === ClientType::Individual
                ? $this->faker->name()
                : $this->faker->company(),
            'fiscal_number' => $this->faker->numerify('##########'),
            'address' => $this->faker->address(),
            'contact' => $this->faker->phoneNumber(),
            'type' => $type,
            'created_by' => null, // Sera fourni par le seeder
            'updated_by' => fn(array $attributes) => $attributes['created_by'],
        ];
    }

    /**
     * Indicate that the client is an individual.
     */
    public function individual(): static
    {
        return $this->state(fn(array $attributes) => [
            'type' => ClientType::Individual,
            'name' => $this->faker->name(),
            'fiscal_number' => $this->faker->numerify('##########'),
        ]);
    }

    /**
     * Indicate that the client is a business.
     */
    public function business(): static
    {
        return $this->state(fn(array $attributes) => [
            'type' => ClientType::Business,
            'name' => $this->faker->company(),
            'fiscal_number' => $this->faker->numerify('##############'),
        ]);
    }
}
