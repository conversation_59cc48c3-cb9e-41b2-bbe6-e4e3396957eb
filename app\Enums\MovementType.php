<?php

namespace App\Enums;

use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;
enum MovementType: string implements HasIcon, HasLabel
{
    case In = 'in';
    case Out = 'out';
    case Adjust = 'adjust';

    public function getLabel(): ?string
    {
        return $this->name;
    }
    public function getIcon(): ?string
    {
        return match ($this->value) {
            self::In->value => 'heroicon-o-arrow-down',
            self::Out->value => 'heroicon-o-arrow-up',
            self::Adjust->value => 'heroicon-o-arrow-path',
        };
    }
}

