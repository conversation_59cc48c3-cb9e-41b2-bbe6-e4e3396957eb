:is(.dark .fi-fo-section),
:is(.dark .fi-section),
:is(.dark .fi-fo-card),
:is(.dark .fi-card),
:is(.dark .fi-fo-group),
:is(.dark .fi-group),
:is(.dark .fi-fo-fieldset),
:is(.dark .fi-fieldset),
:is(.dark .fi-fo-tabs),
:is(.dark .fi-tabs),
:is(.dark .fi-fo-tabs-tab),
:is(.dark .fi-tabs-tab),
:is(.dark .fi-fo-wizard),
:is(.dark .fi-wizard),
:is(.dark .fi-fo-wizard-step),
:is(.dark .fi-wizard-step),
:is(.dark .fi-modal),
:is(.dark .fi-modal-content),
:is(.dark .fi-slideover),
:is(.dark .fi-slideover-content),
:is(.dark .fi-fo-layout),
:is(.dark .fi-layout),
:is(.dark .fi-fo-container),
:is(.dark .fi-container),
:is(.dark .fi-fo-panel),
:is(.dark .fi-panel),
:is(.dark .fi-form),
:is(.dark .fi-form-component-ctn),
:is(.dark .fi-form-section),
:is(.dark .fi-form-card),
:is(.dark .fi-form-group),
:is(.dark .fi-form-fieldset) {
    @apply bg-transparent !important;
    /* background-image: radial-gradient(
        ellipse at top right,
        rgba(var(--primary-950), 1) 0%,
        rgba(var(--primary-950), 0.9) 15%,
        rgba(var(--primary-900), 0.7) 30%,
        rgba(var(--primary-900), 0.5) 45%,
        rgba(var(--primary-950), 0.3) 60%,
        rgba(var(--primary-950), 0.1) 75%,
        rgba(3, 7, 18, 0) 100%
    ); */
}


:is(.dark .fi-ta-table),
:is(.dark .fi-table),
:is(.dark .fi-ta-ctn),
:is(.dark .fi-ta-container),
:is(.dark .fi-ta-content),
:is(.dark .fi-ta-header),
:is(.dark .fi-ta-header-cell),
:is(.dark .fi-ta-header-cell-label),
:is(.dark .fi-ta-header-ctn),
:is(.dark .fi-ta-row),
:is(.dark .fi-ta-cell),
:is(.dark .fi-ta-record),
:is(.dark .fi-ta-col),
:is(.dark .fi-ta-col-wrp),
:is(.dark .fi-ta-text),
:is(.dark .fi-ta-text-item),
:is(.dark .fi-ta-icon),
:is(.dark .fi-ta-icon-item),
:is(.dark .fi-ta-badge),
:is(.dark .fi-ta-badge-item),
:is(.dark .fi-ta-image),
:is(.dark .fi-ta-image-item),
:is(.dark .fi-ta-color),
:is(.dark .fi-ta-color-item),
:is(.dark .fi-ta-toggle),
:is(.dark .fi-ta-toggle-item),
:is(.dark .fi-ta-select),
:is(.dark .fi-ta-select-item),
:is(.dark .fi-ta-text-input),
:is(.dark .fi-ta-text-input-item),
:is(.dark .fi-ta-summary),
:is(.dark .fi-ta-summary-row),
:is(.dark .fi-ta-summary-cell),
:is(.dark .fi-ta-summary-row-heading),
:is(.dark .fi-ta-text-summary),
:is(.dark .fi-ta-actions),
:is(.dark .fi-ta-actions-item),
:is(.dark .fi-ta-bulk-actions),
:is(.dark .fi-ta-filters),
:is(.dark .fi-ta-filters-form),
:is(.dark .fi-ta-search),
:is(.dark .fi-ta-search-field),
:is(.dark .fi-ta-pagination),
:is(.dark .fi-ta-empty-state),
:is(.dark .fi-ta-loading-indicator) {
    @apply bg-transparent !important;
}

/* RadioDeck transparent backgrounds in dark theme */
:is(.dark) label div.bg-white.dark\:bg-gray-900 {
    @apply bg-transparent !important;
}

/* Disable hover effects for RadioDeck cards */
:is(.dark) label div.bg-white.dark\:bg-gray-900:hover {
    @apply bg-transparent !important;
}
