<?php

namespace App\Filament\Company\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\Invoice;
use App\Models\Offering;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Enums\DocumentType;
use App\Enums\InvoiceStatus;
use App\Enums\AdjustmentType;
use Illuminate\Support\Carbon;
use App\Enums\AdjustmentStatus;
use App\Models\DocumentLineItem;
use Filament\Resources\Resource;
use App\Enums\AdjustmentCategory;
use Awcodes\TableRepeater\Header;
use App\Utilities\CurrencyAccessor;
use Illuminate\Support\Facades\Auth;
use App\Enums\DocumentDiscountMethod;
use Filament\Notifications\Notification;
use Guava\FilamentClusters\Forms\Cluster;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use App\Filament\Forms\Components\ClientSelect;
use App\Filament\Forms\Components\CurrencySelect;
use App\Filament\Forms\Components\DocumentTotals;
use App\Filament\Forms\Components\OfferingSelect;
use App\Filament\Forms\Components\AdjustmentSelect;
use App\Filament\Forms\Components\CustomTableRepeater;
use App\Filament\Forms\Components\DocumentFooterSection;
use App\Filament\Forms\Components\DocumentHeaderSection;
use App\Filament\Company\Resources\InvoiceResource\Pages;
use Illuminate\Validation\ValidationException;


class InvoiceResource extends Resource
{
    protected static ?string $model = Invoice::class;

    //protected static ?string $navigationIcon = 'heroicon-o-shopping-cart';


    public static function form(Form $form): Form
    {
        $user = Auth::user();

        if (!$user) {
            throw new \RuntimeException('User must be authenticated to access this resource.');
        }

        $company = $user->currentCompany;

        if (!$company) {
            throw new \RuntimeException('No current company found. Please select a company first.');
        }

        $settings = $company->defaultInvoice;

        return $form
            ->schema([
                DocumentHeaderSection::make('Invoice Header')
                    ->defaultHeader($settings?->header)
                    ->defaultSubheader($settings?->subheader),
                Forms\Components\Section::make('Invoice Details')
                    ->schema([
                        Forms\Components\Split::make([
                            Forms\Components\Group::make([
                                ClientSelect::make('client_id')
                                    ->label('Client')
                                    ->required(),
                                CurrencySelect::make('currency_code'),
                            ]),
                            Forms\Components\Group::make([
                                Forms\Components\TextInput::make('invoice_number')
                                    ->label('Invoice number')
                                    ->default(static fn() => Invoice::getNextDocumentNumber())
                                    ->required()
                                    ->maxLength(50)
                                    ->unique(Invoice::class, 'invoice_number', ignoreRecord: true)
                                    ->regex('/^[A-Za-z0-9\-_]+$/')
                                    ->validationMessages([
                                        'unique' => 'This invoice number already exists.',
                                        'regex' => 'Invoice number can only contain letters, numbers, hyphens, and underscores.',
                                    ]),
                                Cluster::make([
                                    Forms\Components\DatePicker::make('date')
                                        ->label('Invoice date')
                                        ->live()
                                        ->default(now())
                                        ->required()
                                        ->maxDate(now()->addDays(30)) // Empêche les dates trop futures
                                        ->minDate(now()->subYears(2)) // Empêche les dates trop anciennes,
                                ])->label('Invoice date')
                                    ->columns(1),
                                Forms\Components\Select::make('status')
                                    ->label('Status')
                                    ->options(InvoiceStatus::class)
                                    ->default(InvoiceStatus::Draft)
                                    ->required(),
                                Forms\Components\Select::make('discount_method')
                                    ->label('Discount method')
                                    ->options(DocumentDiscountMethod::class)
                                    ->required()
                                    ->default($settings?->discount_method)
                                    ->afterStateUpdated(function ($state, Forms\Set $set) {
                                        $discountMethod = DocumentDiscountMethod::parse($state);

                                        if ($discountMethod?->isPerDocument()) {
                                            $set('lineItems.*.salesDiscounts', []);
                                        }
                                    })
                                    ->live(),
                            ])->grow(true),
                        ])->from('md'),
                        CustomTableRepeater::make('lineItems')
                            ->hiddenLabel()
                            ->relationship('lineItems')
                            ->saveRelationshipsUsing(null)
                            ->dehydrated(true)
                            ->reorderable()
                            ->orderColumn('line_number')
                            ->reorderAtStart()
                            ->cloneable()
                            ->addActionLabel('Add an item')
                            ->headers(function (Forms\Get $get) use ($settings) {
                                $discountMethod = DocumentDiscountMethod::parse($get('discount_method'));
                                $hasDiscounts = $discountMethod?->isPerLineItem() ?? false;

                                $headers = [
                                    Header::make($settings?->resolveColumnLabel('item_name', 'Items') ?? 'Items')
                                        ->width('30%'),
                                    Header::make($settings?->resolveColumnLabel('unit_name', 'Quantity') ?? 'Quantity')
                                        ->width('10%'),
                                    Header::make($settings?->resolveColumnLabel('price_name', 'Price') ?? 'Price')
                                        ->width('10%'),
                                ];

                                if ($hasDiscounts) {
                                    $headers[] = Header::make('Adjustments')->width('30%');
                                } else {
                                    $headers[] = Header::make('Taxes')->width('30%');
                                }

                                $headers[] = Header::make($settings?->resolveColumnLabel('amount_name', 'Amount') ?? 'Amount')
                                    ->width('10%')
                                    ->align('right');

                                return $headers;
                            })
                            ->schema([
                                Forms\Components\Group::make([
                                    OfferingSelect::make('offering_id')
                                        ->label('Item')
                                        ->hiddenLabel()
                                        ->placeholder('Select item')
                                        ->required()
                                        ->live()
                                        ->inlineSuffix()
                                        ->sellable()
                                        ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get, $state, ?DocumentLineItem $record) {
                                            if (!$state) {
                                                return;
                                            }
                                            $offeringId = (int) $state;
                                            // Validation de sécurité : vérifier que l'offering appartient à la bonne entreprise
                                            $user = Auth::user();
                                            if (!$user || !$user->currentCompany) {
                                                return;
                                            }

                                            $discountMethod = DocumentDiscountMethod::parse($get('../../discount_method'));
                                            $isPerLineItem = $discountMethod?->isPerLineItem() ?? false;

                                            $existingTaxIds = [];
                                            $existingDiscountIds = [];

                                            if ($record) {
                                                $existingTaxIds = $record->salesTaxes()->pluck('adjustments.id')->toArray();
                                                if ($isPerLineItem) {
                                                    $existingDiscountIds = $record->salesDiscounts()->pluck('adjustments.id')->toArray();
                                                }
                                            }

                                            $with = [
                                                'salesTaxes' => static function ($query) use ($existingTaxIds) {
                                                    $query->where(static function ($query) use ($existingTaxIds) {
                                                        $query->where('status', AdjustmentStatus::Active)
                                                            ->orWhereIn('adjustments.id', $existingTaxIds);
                                                    });
                                                },
                                            ];

                                            if ($isPerLineItem) {
                                                $with['salesDiscounts'] = static function ($query) use ($existingDiscountIds) {
                                                    $query->where(static function ($query) use ($existingDiscountIds) {
                                                        $query->where('status', AdjustmentStatus::Active)
                                                            ->orWhereIn('adjustments.id', $existingDiscountIds);
                                                    });
                                                };
                                            }

                                            // Sécurité : s'assurer que l'offering appartient à la bonne entreprise
                                            $offeringRecord = Offering::with($with)
                                                ->where('company_id', $user->currentCompany->id)
                                                ->where('sellable', true)
                                                ->find($offeringId);

                                            if (!$offeringRecord) {
                                                return;
                                            }

                                            // Validation du prix
                                            $price = $offeringRecord->price;
                                            if ($price < 0 || $price > *********.99) {
                                                return;
                                            }

                                            $unitPrice = number_format($price, 2, '.', '');

                                            $set('description', $offeringRecord->description ?? '');
                                            $set('unit_price', $unitPrice);
                                            $set('salesTaxes', $offeringRecord->salesTaxes->pluck('id')->toArray());

                                            if ($isPerLineItem) {
                                                $set('salesDiscounts', $offeringRecord->salesDiscounts->pluck('id')->toArray());
                                            }
                                        }),
                                    Forms\Components\Textarea::make('description')
                                        ->placeholder('Enter item description')
                                        ->hiddenLabel(),
                                ])->columnSpan(1),
                                Forms\Components\TextInput::make('quantity')
                                    ->required()
                                    ->numeric()
                                    ->live()
                                    ->minValue(0.01)
                                    ->maxValue(999999.99)
                                    ->step(0.01)
                                    ->default(1)
                                    ->validationMessages([
                                        'min' => 'Quantity must be greater than 0.',
                                        'max' => 'Quantity cannot exceed 999,999.99.',
                                        'numeric' => 'Quantity must be a valid number.',
                                    ]),
                                Forms\Components\TextInput::make('unit_price')
                                    ->hiddenLabel()
                                    ->required()
                                    ->numeric()
                                    ->step(0.01)
                                    ->live()
                                    ->minValue(0)
                                    ->maxValue(*********.99)
                                    ->default(0)
                                    ->validationMessages([
                                        'min' => 'Unit price cannot be negative.',
                                        'max' => 'Unit price cannot exceed 999,999,999.99.',
                                        'numeric' => 'Unit price must be a valid number.',
                                    ]),
                                Forms\Components\Group::make([
                                    AdjustmentSelect::make('salesTaxes')
                                        ->label('Taxes')
                                        ->hiddenLabel()
                                        ->placeholder('Select taxes')
                                        ->category(AdjustmentCategory::Tax)
                                        ->type(AdjustmentType::Sales)
                                        ->adjustmentsRelationship('salesTaxes')
                                        ->saveRelationshipsUsing(null)
                                        ->dehydrated(true)
                                        ->inlineSuffix()
                                        ->preload()
                                        ->multiple()
                                        ->live()
                                        ->searchable(),
                                    AdjustmentSelect::make('salesDiscounts')
                                        ->label('Discounts')
                                        ->hiddenLabel()
                                        ->placeholder('Select discounts')
                                        ->category(AdjustmentCategory::Discount)
                                        ->type(AdjustmentType::Sales)
                                        ->adjustmentsRelationship('salesDiscounts')
                                        ->saveRelationshipsUsing(null)
                                        ->dehydrated(true)
                                        ->inlineSuffix()
                                        ->multiple()
                                        ->live()
                                        ->visible(function (Forms\Get $get) {
                                            $discountMethod = DocumentDiscountMethod::parse($get('../../discount_method'));

                                            return $discountMethod?->isPerLineItem() ?? false;
                                        })
                                        ->searchable(),
                                ])->columnSpan(1),

                                Forms\Components\Placeholder::make('line_total')
                                    ->hiddenLabel()
                                    ->extraAttributes(['class' => 'text-right font-semibold'])
                                    ->content(function (Forms\Get $get) {
                                        $quantity = (float) ($get('quantity') ?? 0);
                                        $unitPrice = (float) ($get('unit_price') ?? 0);

                                        $total = $quantity * $unitPrice;

                                        // Formatage direct avec la devise par défaut
                                        $currencyCode = CurrencyAccessor::getDefaultCurrency();
                                        return money($total, $currencyCode, true)->format();
                                    })
                                    ->live(),
                            ]),

                        DocumentTotals::make()
                            ->type(DocumentType::Invoice),

                    ]),
                Forms\Components\Section::make('Additional Information')
                    ->schema([
                        Forms\Components\Textarea::make('notes')
                            ->label('Notes')
                            ->rows(3)
                            ->maxLength(2000)
                            ->columnSpanFull()
                            ->validationMessages([
                                'max' => 'Notes cannot exceed 2000 characters.',
                            ]),
                    ])
                    ->collapsible()
                    ->collapsed(),
                DocumentFooterSection::make('Invoice Footer')
                    ->defaultFooter($settings?->footer),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->searchable(),
                Tables\Columns\TextColumn::make('date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('invoice_number')
                    ->label('Number')
                    ->searchable()
                    ->description(function (Invoice $record) {
                        return $record->source_type?->getLabel();
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('client.name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('total')
                    ->currency(static fn(Invoice $record) => $record->currency_code)
                    ->sortable()
                    ->toggleable(),
                    //->alignEnd(),
                Tables\Columns\TextColumn::make('amount_due')
                    ->label('Amount due')
                    ->currency(static fn(Invoice $record) => $record->currency_code)
                    ->sortable()
                    //->alignEnd()
                    ->hideOnTabs(['draft']),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('client')
                    ->relationship('client', 'name')
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('status')
                    ->options(InvoiceStatus::class)
                    ->multiple(),
                Tables\Filters\TernaryFilter::make('has_payments')
                    ->label('Has payments')
                    ->queries(
                        true: fn(Builder $query) => $query->whereHas('payments'),
                        false: fn(Builder $query) => $query->whereDoesntHave('payments'),
                    ),
            ])->filtersTriggerAction(
                fn(Tables\Actions\Action $action) => $action
                    ->button()
                    ->label('Filters')
                    ->icon('heroicon-m-funnel')
                    ->slideOver()
            )
            ->headerActions([])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ActionGroup::make([
                        Tables\Actions\EditAction::make()
                            ->url(static fn(Invoice $record) => Pages\EditInvoice::getUrl(['record' => $record])),
                        Tables\Actions\ViewAction::make()
                            ->url(static fn(Invoice $record) => Pages\ViewInvoice::getUrl(['record' => $record])),
                        Tables\Actions\ReplicateAction::make()
                            ->excludeAttributes([
                                'status',
                                'amount_paid',
                                'amount_due',
                                'invoice_number',
                                'date',
                                'due_date',
                                'approved_at',
                                'paid_at',
                                'last_sent_at',
                                'last_viewed_at',
                            ])
                            ->afterReplicaSaved(function (Invoice $replica): void {
                                $replica->update([
                                    'status' => InvoiceStatus::Draft,
                                    'invoice_number' => Invoice::getNextDocumentNumber(),
                                    'date' => now(),
                                    'due_date' => now()->addDays(30),
                                ]);
                            }),
                        Tables\Actions\Action::make('approve_draft')
                            ->label('Approve')
                            ->icon('heroicon-o-check-circle')
                            ->color('success')
                            ->visible(function (Invoice $record) {
                                $user = Auth::user();
                                return $record->status === InvoiceStatus::Draft
                                    && $user
                                    && $user->currentCompany
                                    && $record->company_id === $user->currentCompany->id;
                            })
                            ->requiresConfirmation()
                            ->modalHeading('Approve Invoice')
                            ->modalDescription('Are you sure you want to approve this invoice? This action cannot be undone.')
                            ->action(function (Invoice $record) {
                                $user = Auth::user();

                                // Vérifications de sécurité
                                if (!$user || !$user->currentCompany || $record->company_id !== $user->currentCompany->id) {
                                    throw new \RuntimeException('Unauthorized access to invoice.');
                                }

                                if ($record->status !== InvoiceStatus::Draft) {
                                    throw new \RuntimeException('Only draft invoices can be approved.');
                                }

                                $record->update([
                                    'status' => InvoiceStatus::Sent,
                                    'approved_at' => now(),
                                ]);
                            }),
                        Tables\Actions\Action::make('mark_as_sent')
                            ->label('Mark as sent')
                            ->icon('heroicon-o-paper-airplane')
                            ->color('primary')
                            ->visible(function (Invoice $record) {
                                $user = Auth::user();
                                return $record->status === InvoiceStatus::Draft
                                    && $user
                                    && $user->currentCompany
                                    && $record->company_id === $user->currentCompany->id;
                            })
                            ->requiresConfirmation()
                            ->modalHeading('Mark Invoice as Sent')
                            ->modalDescription('Are you sure you want to mark this invoice as sent?')
                            ->action(function (Invoice $record) {
                                $user = Auth::user();

                                // Vérifications de sécurité
                                if (!$user || !$user->currentCompany || $record->company_id !== $user->currentCompany->id) {
                                    throw new \RuntimeException('Unauthorized access to invoice.');
                                }

                                if ($record->status !== InvoiceStatus::Draft) {
                                    throw new \RuntimeException('Only draft invoices can be marked as sent.');
                                }

                                $record->update([
                                    'status' => InvoiceStatus::Sent,
                                    'last_sent_at' => now(),
                                ]);
                            }),

                    ])->dropdown(false),
                    Tables\Actions\DeleteAction::make()
                        ->requiresConfirmation()
                        ->modalHeading('Delete Invoice')
                        ->modalDescription('Are you sure you want to delete this invoice? This action cannot be undone.')
                        ->before(function (Invoice $record) {
                            $user = Auth::user();

                            // Vérifications de sécurité avant suppression
                            if (!$user || !$user->currentCompany || $record->company_id !== $user->currentCompany->id) {
                                throw new \RuntimeException('Unauthorized access to invoice.');
                            }

                            // Empêcher la suppression des factures payées ou envoyées
                            if ($record->status === InvoiceStatus::Paid || $record->amount_paid > 0) {
                                throw new \RuntimeException('Cannot delete invoices that have been paid.');
                            }
                        }),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation()
                        ->modalHeading('Delete Selected Invoices')
                        ->modalDescription('Are you sure you want to delete the selected invoices? This action cannot be undone.')
                        ->before(function (Collection $records) {
                            $user = Auth::user();

                            if (!$user || !$user->currentCompany) {
                                throw new \RuntimeException('User must be authenticated with a current company.');
                            }

                            // Vérifier que toutes les factures appartiennent à l'entreprise courante
                            $invalidRecords = $records->filter(function (Invoice $record) use ($user) {
                                return $record->company_id !== $user->currentCompany->id;
                            });

                            if ($invalidRecords->isNotEmpty()) {
                                throw new \RuntimeException('Unauthorized access to some invoices.');
                            }

                            // Vérifier qu'aucune facture n'est payée
                            $paidRecords = $records->filter(function (Invoice $record) {
                                return $record->status === InvoiceStatus::Paid || $record->amount_paid > 0;
                            });

                            if ($paidRecords->isNotEmpty()) {
                                throw new \RuntimeException('Cannot delete invoices that have been paid.');
                            }
                        }),

                    Tables\Actions\BulkAction::make('approveDrafts')
                        ->label('Approve')
                        ->icon('heroicon-o-check-circle')
                        ->databaseTransaction()
                        ->requiresConfirmation()
                        ->modalHeading('Approve Selected Invoices')
                        ->modalDescription('Are you sure you want to approve the selected invoices?')
                        ->successNotificationTitle('Invoices approved')
                        ->failureNotificationTitle('Failed to Approve Invoices')
                        ->before(function (Collection $records, Tables\Actions\BulkAction $action) {
                            $user = Auth::user();

                            if (!$user || !$user->currentCompany) {
                                throw new \RuntimeException('User must be authenticated with a current company.');
                            }

                            // Vérifier que toutes les factures appartiennent à l'entreprise courante
                            $invalidRecords = $records->filter(function (Invoice $record) use ($user) {
                                return $record->company_id !== $user->currentCompany->id;
                            });

                            if ($invalidRecords->isNotEmpty()) {
                                Notification::make()
                                    ->title('Approval failed')
                                    ->body('Unauthorized access to some invoices.')
                                    ->persistent()
                                    ->danger()
                                    ->send();
                                $action->cancel(true);
                                return;
                            }

                            // Vérifier que toutes les factures peuvent être approuvées
                            $isInvalid = $records->contains(function (Invoice $record) {
                                return !method_exists($record, 'canBeApproved') || !$record->canBeApproved();
                            });

                            if ($isInvalid) {
                                Notification::make()
                                    ->title('Approval failed')
                                    ->body('Only draft invoices can be approved. Please adjust your selection and try again.')
                                    ->persistent()
                                    ->danger()
                                    ->send();

                                $action->cancel(true);
                            }
                        })
                        ->action(function (Collection $records, Tables\Actions\BulkAction $action) {
                            $records->each(function (Invoice $record) {
                                if (method_exists($record, 'approveDraft')) {
                                    $record->approveDraft();
                                } else {
                                    $record->update([
                                        'status' => InvoiceStatus::Sent,
                                        'approved_at' => now(),
                                    ]);
                                }
                            });

                            $action->success();
                        }),
                    Tables\Actions\BulkAction::make('markAsSent')
                        ->label('Mark as sent')
                        ->icon('heroicon-o-paper-airplane')
                        ->databaseTransaction()
                        ->requiresConfirmation()
                        ->modalHeading('Mark Selected Invoices as Sent')
                        ->modalDescription('Are you sure you want to mark the selected invoices as sent?')
                        ->successNotificationTitle('Invoices sent')
                        ->failureNotificationTitle('Failed to Mark Invoices as Sent')
                        ->before(function (Collection $records, Tables\Actions\BulkAction $action) {
                            $user = Auth::user();

                            if (!$user || !$user->currentCompany) {
                                throw new \RuntimeException('User must be authenticated with a current company.');
                            }

                            // Vérifier que toutes les factures appartiennent à l'entreprise courante
                            $invalidRecords = $records->filter(fn(Invoice $record) => $record->company_id !== $user->currentCompany->id);

                            if ($invalidRecords->isNotEmpty()) {
                                Notification::make()
                                    ->title('Sending failed')
                                    ->body('Unauthorized access to some invoices.')
                                    ->persistent()
                                    ->danger()
                                    ->send();
                                $action->cancel(true);
                                return;
                            }

                            // Vérifier que toutes les factures peuvent être marquées comme envoyées
                            $isInvalid = $records->contains(function (Invoice $record) {
                                return !method_exists($record, 'canBeMarkedAsSent') || !$record->canBeMarkedAsSent();
                            });

                            if ($isInvalid) {
                                Notification::make()
                                    ->title('Sending failed')
                                    ->body('Only unsent invoices can be marked as sent. Please adjust your selection and try again.')
                                    ->persistent()
                                    ->danger()
                                    ->send();

                                $action->cancel(true);
                            }
                        })
                        ->action(function (Collection $records, Tables\Actions\BulkAction $action) {
                            $records->each(function (Invoice $record) {
                                if (method_exists($record, 'markAsSent')) {
                                    $record->markAsSent();
                                } else {
                                    $record->update([
                                        'status' => InvoiceStatus::Sent,
                                        'last_sent_at' => now(),
                                    ]);
                                }
                            });

                            $action->success();
                        }),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInvoices::route('/'),
            //'record-payments' => Pages\RecordPayments::route('/record-payments'),
            'create' => Pages\CreateInvoice::route('/create'),
            'view' => Pages\ViewInvoice::route('/{record}'),
            'edit' => Pages\EditInvoice::route('/{record}/edit'),
        ];
    }

    public static function getWidgets(): array
    {
        return [
            //Widgets\InvoiceOverview::class,
        ];
    }
}
