<?php

namespace App\Services;

use App\Models\User;
use App\Models\Company;
use App\Models\CompanyDefault;
use App\Events\CompanyConfigured;
use App\Services\CompanySettingsService;
use App\Utilities\ConfigureCurrencies;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;

class CompanyDefaultService
{
    /**
     * Crée et configure tous les paramètres par défaut d'une entreprise
     */
    public function createCompanyDefaults(Company $company, User $user, string $currencyCode, string $countryCode, string $language): void
    {
        DB::transaction(function () use ($user, $company, $currencyCode, $countryCode, $language) {
            // 1. Créer l'enregistrement CompanyDefault
            $this->createCompanyDefaultRecord($company, $user, $currencyCode, $language);

            // 2. Configurer les services et paramètres d'environnement
            $this->configureEnvironmentSettings($company, $language);

            // 3. Synchroniser les devises
            $this->configureCurrencies();

            // 4. Invalider le cache des paramètres
            $this->invalidateSettingsCache($company->id);

            // 5. Dispatcher l'événement de configuration
            CompanyConfigured::dispatch($company);
        });
    }

    /**
     * Crée ou met à jour l'enregistrement dans la table company_defaults
     */
    private function createCompanyDefaultRecord(Company $company, User $user, string $currencyCode, string $language): void
    {
        // Récupérer les paramètres de localisation depuis l'entreprise
        $locale = $company->locale;

        // Utiliser updateOrCreate pour éviter les doublons
        CompanyDefault::createOrUpdateForCompany($company->id, [
            'currency_code' => $currencyCode,
            'language' => $language,
            'timezone' => $locale?->timezone,
            'date_format' => $locale?->date_format?->value,
            'week_start' => $locale?->week_start?->value,
            'created_by' => $user->id,
            'updated_by' => $user->id,
        ]);
    }

    /**
     * Configure les paramètres d'environnement de l'application
     */
    private function configureEnvironmentSettings(Company $company, string $language): void
    {
        $locale = $company->locale;

        if ($locale) {
            // Configurer la langue
            app()->setLocale($language);
            locale_set_default($language);

            // Configurer le fuseau horaire
            if ($locale->timezone) {
                Config::set('app.timezone', $locale->timezone);
                date_default_timezone_set($locale->timezone);
            }
        }
    }

    /**
     * Synchronise les devises avec la configuration
     */
    private function configureCurrencies(): void
    {
        ConfigureCurrencies::syncCurrencies();
    }

    /**
     * Invalide le cache des paramètres de l'entreprise
     */
    private function invalidateSettingsCache(int $companyId): void
    {
        CompanySettingsService::invalidateSettings($companyId);
    }
}
