<?php

namespace App\Models;

use App\Traits\Blamable;
use App\Enums\PaymentMethod;
use App\Traits\CompanyOwned;
use Illuminate\Database\Eloquent\Model;
use Database\Factories\TransactionFactory;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Transaction extends Model
{
    /** @use HasFactory<\Database\Factories\TransactionFactory> */
    use HasFactory;

    use Blamable;
    use CompanyOwned;
    use HasFactory;

    protected $fillable = [
        'company_id',
        'transaction_id',
        'contact',
        'payment_channel',
        'payment_method',
        'is_payment',
        'reference',
        'amount',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'payment_method' => PaymentMethod::class,
        'amount' => 'integer',
    ];

    public function transactionable(): MorphTo
    {
        return $this->morphTo();
    }

    protected static function newFactory(): Factory
    {
        return TransactionFactory::new();
    }

}
