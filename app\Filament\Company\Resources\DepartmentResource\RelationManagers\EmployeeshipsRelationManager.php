<?php

namespace App\Filament\Company\Resources\DepartmentResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class EmployeeshipsRelationManager extends RelationManager
{
    protected static string $relationship = 'employeeships';

    protected static ?string $title = 'Department Employees';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->relationship('user', 'name')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->placeholder('Select employee'),

                Forms\Components\TextInput::make('job_title')
                    ->placeholder('Job title')
                    ->maxLength(255),

                Forms\Components\Select::make('role')
                    ->options([
                        'employee' => 'Employee',
                        'manager' => 'Manager',
                        'admin' => 'Admin',
                        'hr' => 'HR',
                    ])
                    ->placeholder('Select role'),

                Forms\Components\Select::make('employment_type')
                    ->options([
                        'full_time' => 'Full Time',
                        'part_time' => 'Part Time',
                        'contract' => 'Contract',
                        'intern' => 'Intern',
                        'temporary' => 'Temporary',
                    ])
                    ->placeholder('Select employment type'),

                Forms\Components\DatePicker::make('hire_date')
                    ->native(false)
                    ->placeholder('Hire date'),

                Forms\Components\DatePicker::make('start_date')
                    ->native(false)
                    ->placeholder('Start date'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('user.name')
            ->columns([
                Tables\Columns\ImageColumn::make('photo')
                    ->circular()
                    ->size(40),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('Employee Name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('job_title')
                    ->searchable()
                    ->sortable()
                    ->placeholder('No job title'),

                Tables\Columns\TextColumn::make('role')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'admin' => 'danger',
                        'manager' => 'warning',
                        'hr' => 'info',
                        'employee' => 'success',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('employment_type')
                    ->badge()
                    ->color('primary'),

                Tables\Columns\TextColumn::make('hire_date')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('start_date')
                    ->date()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('role')
                    ->options([
                        'employee' => 'Employee',
                        'manager' => 'Manager',
                        'admin' => 'Admin',
                        'hr' => 'HR',
                    ]),

                Tables\Filters\SelectFilter::make('employment_type')
                    ->options([
                        'full_time' => 'Full Time',
                        'part_time' => 'Part Time',
                        'contract' => 'Contract',
                        'intern' => 'Intern',
                        'temporary' => 'Temporary',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\AttachAction::make()
                    ->preloadRecordSelect(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DetachAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DetachBulkAction::make(),
                ]),
            ])
            ->defaultSort('user.name', 'asc');
    }
}
