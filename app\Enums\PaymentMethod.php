<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;
enum PaymentMethod: string implements HasLabel
{
    case Cash = 'cash';
    case Transfer = 'transfer';
    case Other = 'other';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Cash => 'Cash On Hand',
            self::Transfer => 'Transfer',
            self::Other => 'Other',
        };
    }
}

