<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\DocumentDefault;
use App\Enums\DocumentType;
use App\Enums\Template;
use App\Enums\Font;
use Illuminate\Database\Seeder;

class DocumentDefaultEnsureSeeder extends Seeder
{
    /**
     * S'assurer que chaque entreprise a ses paramètres par défaut pour tous les types de documents
     * Ce seeder peut être exécuté plusieurs fois sans problème
     */
    public function run(): void
    {
        $companies = Company::all();
        $documentTypes = [
            DocumentType::Invoice,
            DocumentType::Estimate,
            DocumentType::Delivery,
            DocumentType::Bill,
        ];

        $this->command->info("🔍 Vérification des paramètres par défaut pour {$companies->count()} entreprises...");

        $created = 0;
        $existing = 0;

        foreach ($companies as $company) {
            foreach ($documentTypes as $type) {
                $exists = DocumentDefault::where('company_id', $company->id)
                    ->where('type', $type)
                    ->exists();

                if (!$exists) {
                    $this->createDocumentDefault($company, $type);
                    $created++;
                    $this->command->info("   ✅ Créé: {$company->name} - {$type->getLabel()}");
                } else {
                    $existing++;
                }
            }
        }

        $this->command->info("📊 Résumé:");
        $this->command->info("   • Paramètres créés: {$created}");
        $this->command->info("   • Paramètres existants: {$existing}");
        $this->command->info("✅ Toutes les entreprises ont maintenant leurs paramètres par défaut !");
    }

    private function createDocumentDefault(Company $company, DocumentType $type): void
    {
        $defaultData = $this->getDefaultDataForType($type);
        $fieldLabels = $this->getFieldLabelsForType($type);

        DocumentDefault::withoutTimestamps(function () use ($company, $type, $defaultData, $fieldLabels) {
            DocumentDefault::create(array_merge([
                'company_id' => $company->id,
                'type' => $type,
                'logo' => null,
                'qr_code' => null,
                'show_logo' => true,
                'show_qr_code' => false,
                'number_prefix' => $type->Prefix(),
                'header' => $type->Header(),
                'subheader' => 'Document commercial',

                // Clés de traduction pour les libellés clients
                'client_name' => [
                    'option' => 'client',
                    'custom' => null,
                ],
                'client_register' => [
                    'option' => 'client_no',
                    'custom' => null,
                ],
                'client_address' => [
                    'option' => 'address',
                    'custom' => null,
                ],
                'object_name' => [
                    'option' => 'subject',
                    'custom' => null,
                ],

                // Sections à afficher selon le type de document
                'show_header' => true,
                'show_client_details' => true,
                'show_objet_details' => true,
                'show_company_details' => true,
                'show_lines' => true,
                'show_totals' => $defaultData['show_totals'],
                'show_discount' => $defaultData['show_discount'],
                'show_tax' => $defaultData['show_tax'],
                'show_payment_details' => $defaultData['show_payment_details'],
                'show_notes' => true,
                'show_signature' => false,
                'show_footer' => true,

                // Méthode de remise et devise
                'discount_method' => \App\Enums\DocumentDiscountMethod::PerDocument,
                'currency_code' => 'XOF',

                // Apparence
                'template' => Template::Default,
                'font' => Font::Inter,
                'accent_color' => '#3b82f6',

                // Audit
                'created_by' => $company->user_id,
                'updated_by' => $company->user_id,
            ], $fieldLabels));
        });
    }

    private function getDefaultDataForType(DocumentType $type): array
    {
        return match ($type) {
            DocumentType::Invoice => [
                'show_totals' => true,
                'show_discount' => true,
                'show_tax' => true,
                'show_payment_details' => true,
            ],
            DocumentType::Estimate => [
                'show_totals' => true,
                'show_discount' => true,
                'show_tax' => true,
                'show_payment_details' => false,
            ],
            DocumentType::Delivery => [
                'show_totals' => false,
                'show_discount' => false,
                'show_tax' => false,
                'show_payment_details' => false,
            ],
            DocumentType::Bill => [
                'show_totals' => true,
                'show_discount' => true,
                'show_tax' => true,
                'show_payment_details' => true,
            ],
        };
    }

    /**
     * Obtenir les libellés de champs spécifiques à chaque type de document
     */
    private function getFieldLabelsForType(DocumentType $type): array
    {
        $baseFields = [
            'item_name' => [
                'option' => 'items',
                'custom' => null,
            ],
        ];

        if ($type === DocumentType::Delivery) {
            // Champs spécifiques aux documents de livraison
            return array_merge($baseFields, [
                'delivery_person' => [
                    'option' => 'delivery',
                    'custom' => null,
                ],
                'receiver_person' => [
                    'option' => 'receiver',
                    'custom' => null,
                ],
                'remarks' => [
                    'option' => 'remarks',
                    'custom' => null,
                ],
                // Mettre les champs standards à null pour les livraisons
                'unit_name' => null,
                'price_name' => null,
                'amount_name' => null,
                'final_amount' => null,
                'payment' => null,
            ]);
        }

        // Champs pour les documents commerciaux standards (factures, devis, etc.)
        return array_merge($baseFields, [
            'unit_name' => [
                'option' => 'quantity',
                'custom' => null,
            ],
            'price_name' => [
                'option' => 'price',
                'custom' => null,
            ],
            'amount_name' => [
                'option' => 'amount',
                'custom' => null,
            ],
            'final_amount' => [
                'option' => 'total',
                'custom' => null,
            ],
            'payment' => [
                'option' => 'payment',
                'custom' => null,
            ],
            // Mettre les champs de livraison à null pour les documents standards
            'delivery_person' => null,
            'receiver_person' => null,
            'remarks' => null,
        ]);
    }
}
