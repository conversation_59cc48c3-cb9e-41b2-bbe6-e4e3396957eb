<?php

namespace App\Filament\Company\Resources\EstimateResource\Pages;

use App\Filament\Company\Resources\EstimateResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListEstimates extends ListRecords
{
    protected static string $resource = EstimateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
