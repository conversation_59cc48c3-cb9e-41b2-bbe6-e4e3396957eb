<?php

namespace App\Enums;

use App\Enums\Concerns\ParsesEnum;
use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;

enum EstimateStatus: string implements HasColor, HasLabel
{
    use ParsesEnum;

    case Draft = 'draft';
    case Sent = 'sent';
    case Unsent = 'unsent';
    case Viewed = 'viewed';
    case Accepted = 'accepted';
    case Declined = 'declined';
    case Expired = 'expired';
    case Converted = 'converted';

    public function getLabel(): ?string
    {
        return $this->name;
    }

    public function getColor(): string|array|null
    {
        return match ($this) {
            self::Unsent , self::Draft => 'gray',
            self::Sent, self::Viewed => 'primary',
            self::Accepted, self::Converted => 'success',
            self::Declined => 'danger',
            self::Expired => 'warning',
        };
    }

    public static function getActiveStatuses(): array
    {
        return [
            self::Draft,
            self::Unsent,
            self::Sent,
            self::Viewed,
        ];
    }

    public static function getCompletedStatuses(): array
    {
        return [
            self::Accepted,
            self::Declined,
            self::Expired,
            self::Converted,
            self::Unsent
        ];
    }

    public function isActive(): bool
    {
        return in_array($this, self::getActiveStatuses());
    }

    public function isCompleted(): bool
    {
        return in_array($this, self::getCompletedStatuses());
    }
}
