<?php

namespace App\Traits;

use App\Enums\AdjustmentComputation;
use App\Enums\DocumentDiscountMethod;
use App\Models\DocumentLineItem;
use App\Utilities\RateCalculator;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

trait ManagesLineItems
{
    protected function handleLineItems(Model $record, Collection $lineItems): void
    {
        // D'abord, supprimer les lineItems qui ne sont plus présents
        $this->deleteRemovedLineItems($record, $lineItems);

        // Ensuite, traiter chaque lineItem (création ou mise à jour)
        foreach ($lineItems as $index => $itemData) {
            $lineItem = isset($itemData['id'])
                ? $record->lineItems->find($itemData['id'])
                : $record->lineItems()->make();

            // Si le lineItem n'existe pas dans la collection actuelle, le créer
            if (!$lineItem) {
                $lineItem = $record->lineItems()->make();
            }

            // Préparer les données pour la sauvegarde
            $quantity = (float) ($itemData['quantity'] ?? 1);
            $unitPrice = (float) ($itemData['unit_price'] ?? 0);

            $lineItem->fill([
                'offering_id' => $itemData['offering_id'],
                'description' => $itemData['description'],
                'quantity' => $quantity,
                'unit_price' => $unitPrice, // Valeur décimale directe
                'line_number' => $index + 1,
            ]);

            if (!$lineItem->exists) {
                $lineItem->documentable()->associate($record);
            }

            $lineItem->save();

            // Gérer les ajustements (taxes et rabais)
            $this->handleLineItemAdjustments($lineItem, $itemData);

            // Calculer et sauvegarder les totaux du lineItem
            $this->updateLineItemTotals($lineItem, $record->discount_method);
        }
    }

    protected function deleteRemovedLineItems(Model $record, Collection $lineItems): void
    {
        $existingLineItemIds = $record->lineItems->pluck('id');
        $updatedLineItemIds = $lineItems->pluck('id')->filter();
        $lineItemsToDelete = $existingLineItemIds->diff($updatedLineItemIds);

        if ($lineItemsToDelete->isNotEmpty()) {
            $record
                ->lineItems()
                ->whereIn('id', $lineItemsToDelete)
                ->each(fn(DocumentLineItem $lineItem) => $lineItem->delete());
        }
    }

    protected function handleLineItemAdjustments(DocumentLineItem $lineItem, array $itemData): void
    {
        // Gérer les taxes de vente
        $salesTaxIds = collect($itemData['salesTaxes'] ?? [])
            ->filter()
            ->unique();

        if ($salesTaxIds->isNotEmpty()) {
            $lineItem->salesTaxes()->sync($salesTaxIds);
        }

        // Gérer les remises de vente si présentes
        $salesDiscountIds = collect($itemData['salesDiscounts'] ?? [])
            ->filter()
            ->unique();

        if ($salesDiscountIds->isNotEmpty()) {
            $lineItem->salesDiscounts()->sync($salesDiscountIds);
        }

        $lineItem->refresh();
    }

    protected function updateLineItemTotals(DocumentLineItem $lineItem, DocumentDiscountMethod $discountMethod): void
    {
        // Calculer les totaux en valeurs décimales
        $taxTotal = $lineItem->calculateTaxTotalAmount();
        $discountTotal = $discountMethod->isPerLineItem()
            ? $lineItem->calculateDiscountTotalAmount()
            : 0.0;

        $lineItem->updateQuietly([
            'tax_total' => $taxTotal,      // Valeur décimale directe
            'discount_total' => $discountTotal, // Valeur décimale directe
        ]);
    }

    protected function updateDocumentTotals(Model $record, array $data): array
    {
        // Recalculer les totaux depuis les lineItems actuels
        $record->refresh(); // S'assurer d'avoir les dernières données

        // Calculer les totaux en valeurs décimales
        $subtotal = 0;
        $taxTotal = 0;

        foreach ($record->lineItems as $lineItem) {
            $lineSubtotal = $lineItem->quantity * $lineItem->unit_price;
            $subtotal += $lineSubtotal;
            $taxTotal += $lineItem->tax_total;
        }

        $discountTotal = $this->calculateDiscountTotal(
            DocumentDiscountMethod::parse($data['discount_method']),
            AdjustmentComputation::parse($data['discount_computation']),
            $data['discount_rate'] ?? null,
            $subtotal,
            $record,
        );

        $grandTotal = $subtotal + $taxTotal - $discountTotal;

        return [
            'subtotal' => $subtotal,
            'tax_total' => $taxTotal,
            'discount_total' => $discountTotal,
            'total' => $grandTotal,
        ];
    }

    protected function calculateDiscountTotal(
        DocumentDiscountMethod $discountMethod,
        ?AdjustmentComputation $discountComputation,
        ?string $discountRate,
        float $subtotal,
        Model $record
    ): float {
        if ($discountMethod->isPerLineItem()) {
            // Calculer la somme des rabais des lineItems
            $discountTotal = 0;
            foreach ($record->lineItems as $lineItem) {
                $discountTotal += $lineItem->discount_total;
            }
            return $discountTotal;
        }

        // Rabais global sur le document
        if ($discountComputation?->isPercentage()) {
            $rate = (float) ($discountRate ?? 0);
            // Utiliser RateCalculator pour les calculs de pourcentage précis
            $scaledRate = RateCalculator::decimalToScaledRate($rate);
            return RateCalculator::scaledRateToDecimal(
                RateCalculator::calculatePercentage(
                    RateCalculator::decimalToScaledRate($subtotal),
                    $scaledRate
                )
            );
        }

        return (float) ($discountRate ?? 0);
    }
}
