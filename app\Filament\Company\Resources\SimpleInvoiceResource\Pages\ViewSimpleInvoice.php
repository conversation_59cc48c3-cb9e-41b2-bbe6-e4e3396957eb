<?php

namespace App\Filament\Company\Resources\SimpleInvoiceResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use App\Filament\Company\Resources\SimpleInvoiceResource;

class ViewSimpleInvoice extends ViewRecord
{
    protected static string $resource = SimpleInvoiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
