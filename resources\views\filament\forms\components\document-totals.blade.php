@php

    use App\View\Models\DocumentTotalViewModel;
    $data = $this->form->getRawState();
    $type = $getType();
    $viewModel = new DocumentTotalViewModel($data, $type);
    extract($viewModel->buildViewData(), EXTR_SKIP);
@endphp

<div class="totals-summary w-full lg:pl-[4rem] lg:pr-[6rem] py-8 lg:py-0">
    <table class="w-full text-right table-fixed">
        <colgroup>
            <col class="w-[25%]"> {{-- Items --}}
            <col class="w-[10%]"> {{-- Quantity --}}
            <col class="w-[10%]"> {{-- Price --}}
            <col class="w-[15%]">
            <col class="w-[20%]"> {{-- Adjustments --}}
            <col class="w-[20%]"> {{-- Amount --}}
        </colgroup>
        <tbody>
            @if ($subtotal)
                <tr>
                    <td colspan="4"></td>
                    <td class="text-sm p-2 font-semibold text-gray-950 dark:text-white whitespace-nowrap">Subtotal:</td>
                    <td class="text-sm p-2 whitespace-nowrap">{{ $subtotal }}</td>
                </tr>
            @endif
            @if ($taxTotal)
                <tr>
                    <td colspan="4"></td>
                    <td class="text-sm p-2 whitespace-nowrap">Tax:</td>
                    <td class="text-sm p-2 whitespace-nowrap">{{ $taxTotal }}</td>
                </tr>
            @endif
            @if ($isPerDocumentDiscount)
                <tr>
                    <td colspan="4"></td>
                    <td class="text-sm p-2 whitespace-nowrap">Discount:</td>
                    <td class="text-sm p-2 whitespace-nowrap">({{ $discountTotal }})</td>
                </tr>
                <tr>
                    <td colspan="4"></td>
                    <td colspan="2" class="text-sm p-2">
                        <div class="flex justify-between space-x-2">
                            @foreach ($getChildComponentContainer()->getComponents() as $component)
                                <div class="flex-1 text-left">{{ $component }}</div>
                            @endforeach
                        </div>
                    </td>
                </tr>
            @elseif($discountTotal)
                <tr>
                    <td colspan="4"></td>
                    <td class="text-sm p-2 whitespace-nowrap">Discount:</td>
                    <td class="text-sm p-2 whitespace-nowrap">({{ $discountTotal }})</td>
                </tr>
            @endif
            <tr>
                <td colspan="4"></td>
                <td class="text-sm p-2 font-semibold text-gray-950 dark:text-white whitespace-nowrap">
                    {{ $amountDue ? 'Total' : 'Grand Total' }}:</td>
                <td class="text-sm p-2 whitespace-nowrap">{{ $grandTotal }}</td>
            </tr>
            @if ($amountDue)
                <tr>
                    <td colspan="4"></td>
                    <td
                        class="text-sm p-2 font-semibold text-gray-950 dark:text-white border-t-4 border-double whitespace-nowrap">
                        Amount
                        Due
                        ({{ $currencyCode }}):
                    </td>
                    <td class="text-sm p-2 border-t-4 border-double whitespace-nowrap">{{ $amountDue }}</td>
                </tr>
            @endif

        </tbody>
    </table>

    <!-- Mobile View -->
    <div class="block lg:hidden">
        <div class="flex flex-col space-y-6">
            @if ($subtotal)
                <div class="flex justify-between items-center">
                    <span class="text-sm font-semibold text-gray-950 dark:text-white">Subtotal:</span>
                    <span class="text-sm">{{ $subtotal }}</span>
                </div>
            @endif
            @if ($taxTotal)
                <div class="flex justify-between items-center">
                    <span class="text-sm">Tax:</span>
                    <span class="text-sm">{{ $taxTotal }}</span>
                </div>
            @endif
            @if ($isPerDocumentDiscount)
                <div class="flex flex-col space-y-2">
                    <span class="text-sm">Discount:</span>
                    <div class="flex justify-between space-x-2">
                        @foreach ($getChildComponentContainer()->getComponents() as $component)
                            <div class="w-1/2">{{ $component }}</div>
                        @endforeach
                    </div>
                </div>
            @elseif($discountTotal)
                <div class="flex justify-between items-center">
                    <span class="text-sm">Discount:</span>
                    <span class="text-sm">({{ $discountTotal }})</span>
                </div>
            @endif
            <div class="flex justify-between items-center">
                <span
                    class="text-sm font-semibold text-gray-950 dark:text-white">{{ $amountDue ? 'Total' : 'Grand Total' }}:</span>
                <span class="text-sm">{{ $grandTotal }}</span>
            </div>
            @if ($amountDue)
                <div class="flex justify-between items-center">
                    <span class="text-sm font-semibold text-gray-950 dark:text-white">Amount Due
                        ({{ $currencyCode }}):</span>
                    <span class="text-sm">{{ $amountDue }}</span>
                </div>
            @endif

        </div>
    </div>
</div>
