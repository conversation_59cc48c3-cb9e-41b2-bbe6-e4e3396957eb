<?php

namespace App\Models;

use App\Enums\ClientType;
use App\Traits\Blamable;
use App\Traits\CompanyOwned;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Client extends Model
{
    /** @use HasFactory<\Database\Factories\ClientFactory> */
    use HasFactory;

    use Blamable;
    use CompanyOwned;

    protected $fillable = [
        'company_id',
        'name',
        'fiscal_number',
        'address',
        'contact',
        'type',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'type' => ClientType::class,
    ];

    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    public function contacts(): MorphMany
    {
        return $this->morphMany(Contact::class, 'contactable');
    }

    public function addresses(): MorphMany
    {
        return $this->morphMany(Address::class, 'addressable');
    }

}
