<?php

namespace App\Filament\Forms\Components;

use App\Utilities\CurrencyAccessor;
use Filament\Forms\Components\TextInput;

class CurrencySelect extends TextInput
{
    protected function setUp(): void
    {
        parent::setUp();

        // Toujours utiliser la devise par défaut du système
        $defaultCurrency = CurrencyAccessor::getDefaultCurrency();

        $this->label('Currency')
            ->default($defaultCurrency)
            ->disabled()
            ->dehydrated()
            ->required();
    }

}
