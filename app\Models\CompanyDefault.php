<?php

namespace App\Models;

use App\Traits\Blamable;
use App\Traits\CompanyOwned;
use Illuminate\Database\Eloquent\Model;
use Database\Factories\CompanyDefaultFactory;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CompanyDefault extends Model
{
    use Blamable;
    use CompanyOwned;
    use HasFactory;

    protected $table = 'company_defaults';

    protected $fillable = [
        'company_id',
        'language',
        'timezone',
        'date_format',
        'week_start',
        'currency_code',
        'created_by',
        'updated_by',
    ];

    public static function createOrUpdateForCompany(int $companyId, array $data): self
    {
        return static::updateOrCreate(
            ['company_id' => $companyId],
            $data
        );
    }

    protected static function booted(): void
    {
    }

    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'currency_code', 'code');
    }
}
