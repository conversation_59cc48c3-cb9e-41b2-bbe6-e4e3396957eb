<?php

namespace App\Filament\Company\Resources\EstimateResource\Pages;

use App\Filament\Company\Resources\EstimateResource;
use App\Traits\ManagesLineItems;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditEstimate extends EditRecord
{
    use ManagesLineItems;

    protected static string $resource = EstimateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Charger les lineItems pour l'édition
        $data['lineItems'] = $this->record->lineItems->map(function ($lineItem) {
            return [
                'id' => $lineItem->id,
                'offering_id' => $lineItem->offering_id,
                'description' => $lineItem->description,
                'quantity' => $lineItem->quantity,
                'unit_price' => $lineItem->unit_price,
                'salesTaxes' => $lineItem->salesTaxes->pluck('id')->toArray(),
                'salesDiscounts' => $lineItem->salesDiscounts->pluck('id')->toArray(),
            ];
        })->toArray();

        return $data;
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        // Sauvegarder d'abord les données principales du devis (sans les lineItems)
        $mainData = collect($data)->except(['lineItems'])->toArray();
        $record->update($mainData);

        // Gérer les lineItems (création, mise à jour et suppression)
        $this->handleLineItems($record, collect($data['lineItems'] ?? []));

        // Calculer et sauvegarder les totaux globaux en même temps
        $totals = $this->updateDocumentTotals($record, $data);
        $record->updateQuietly($totals);

        return $record;
    }
}
