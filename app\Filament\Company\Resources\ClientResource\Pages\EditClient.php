<?php

namespace App\Filament\Company\Resources\ClientResource\Pages;

use Filament\Actions;
use App\Traits\HandlePageRedirect;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Company\Resources\ClientResource;

class EditClient extends EditRecord
{
    use HandlePageRedirect;

    protected static string $resource = ClientResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
