@props([
   'color' => 'currentColor',
   'text' => '',
])

<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 72" aria-hidden="true" fill="none" {{ $attributes }}>
    <g stroke="{{ $color }}" stroke-width="2">
        <path
            d="M20 57.038v-42.076c.33.025.664.038 1 .038 7.18 0 13-5.82 13-13 0-.336-.013-.67-.038-1h172.076c-.025.33-.038.664-.038 1 0 7.18 5.82 13 13 13 .336 0 .67-.013 1-.038v42.076c-.33-.025-.664-.038-1-.038-7.18 0-13 5.82-13 13 0 .336.013.67.038 1h-172.076c.025-.33.038-.664.038-1 0-7.18-5.82-13-13-13-.336 0-.67.013-1 .038z"/>
        <path
            d="M26 51.503v-31.007c.33.024.664.037 1 .037 7.18 0 13-5.626 13-12.567 0-.325.013-.648-.038-.967h160.076c-.025.319-.038.641-.038.967 0 6.94 5.82 12.567 13 12.567.336 0 .67-.012 1-.037v31.007c-.33-.024-.664-.037-1-.037-7.18 0-13 5.626-13 12.567 0 .325.013.648.038.967h-160.076c.025-.319.038-.641.038-.967 0-6.94-5.82-12.567-13-12.567-.336 0-.67.012-1 .037z"/>
    </g>
    <text
        x="50%"
        y="50%"
        text-anchor="middle"
        dominant-baseline="middle"
        class="text-3xl font-light"
        fill="{{ $color }}"
    >{{ $text }}</text>
</svg>
