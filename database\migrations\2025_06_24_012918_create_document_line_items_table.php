<?php
use Illuminate\Database\Migrations\Migration;

use Illuminate\Database\Schema\Blueprint;

use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('document_line_items', function (Blueprint $table) {
            $table->id();
            $table->integer('line_number')->nullable();
            $table->foreignId('company_id')->constrained()->cascadeOnDelete();
            $table->morphs('documentable');
            $table->foreignId('offering_id')->nullable()->constrained()->nullOnDelete();
            $table->bigInteger('quantity')->nullable();
            $table->string('description')->nullable();
            $table->bigInteger('unit_price')->default(0);
            $table->bigInteger('subtotal')->storedAs('quantity * unit_price');
            $table->bigInteger('total')->storedAs('(quantity * unit_price) + tax_total - discount_total');
            $table->bigInteger('tax_total')->default(0);
            $table->bigInteger('discount_total')->default(0);
            $table->string('delivery_person')->nullable();
            $table->string('receiver_person')->nullable();
            $table->text('delivery_remarks')->nullable();
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('document_line_items');
    }
};
