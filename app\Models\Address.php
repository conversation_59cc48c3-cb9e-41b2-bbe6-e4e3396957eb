<?php

namespace App\Models;

use App\Traits\Blamable;
use App\Enums\AddressType;
use App\Traits\CompanyOwned;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Address extends Model
{
    /** @use HasFactory<\Database\Factories\AddressFactory> */
    use HasFactory;

    use Blamable;
    use CompanyOwned;
    use HasFactory;

    protected $table = 'addresses';

    protected $fillable = [
        'company_id',
        'type',
        'recipient',
        'phone',
        'address_line_1',
        'address_line_2',
        'city',
        'state_id',
        'postal_code',
        'country_code',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'type' => AddressType::class,
    ];

    public function addressable(): MorphTo
    {
        return $this->morphTo();
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_code', 'id');
    }

    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_id', 'id');
    }

    protected function addressString(): Attribute
    {
        return Attribute::get(function () {
            $street = array_filter([
                $this->address_line_1,
                $this->address_line_2,
            ]);

            $global = array_filter([
                $this->phone,
                $this->city->name,
            ]);

            return array_filter([
                implode(', ', $street), // Street 1 & 2 on same line if both exist
                implode(', ', $global), // Street 1 & 2 on same line if both exist
                implode(', ', array_filter([
                    $this->postal_code,
                    $this->country->name,
                ])),
            ]);
        });
    }

    public function isIncomplete(): bool
    {
        return empty($this->address_line_1) || empty($this->country);
    }
}
