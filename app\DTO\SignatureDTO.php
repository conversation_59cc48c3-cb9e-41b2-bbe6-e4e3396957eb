<?php

namespace App\DTO;

readonly class SignatureDTO
{
    public function __construct(
        public ?string $role,
        public ?string $name,
        public ?string $image,
    ) {
    }

    public static function fromSettings(array $roleData = null, array $nameData = null, ?string $image = null): self
    {
        // Extraire le rôle depuis les données
        $role = null;
        if ($roleData) {
            $role = $roleData['custom'] ?? $roleData['option'] ?? null;
        }

        // Extraire le nom depuis les données
        $name = null;
        if ($nameData) {
            $name = $nameData['custom'] ?? $nameData['option'] ?? null;
        }

        return new self(
            role: $role,
            name: $name,
            image: $image,
        );
    }

    public static function fake(): self
    {
        return new self(
            role: 'Le Directeur DES RESHUM',
            name: '<PERSON> DAVID GISE SON',
            image: null,
        );
    }

    public function hasSignature(): bool
    {
        return !empty($this->role) || !empty($this->name) || !empty($this->image);
    }

    public function toArray(): array
    {
        return [
            'role' => $this->role,
            'name' => $this->name,
            'image' => $this->image,
        ];
    }
}
