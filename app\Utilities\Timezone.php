<?php

namespace App\Utilities;

use App\Enums\TimeFormat;
use App\Models\Localization;
use DateTimeZone;
use Symfony\Component\Intl\Timezones;

class Timezone
{
    /**
     * Retourne une liste de fuseaux horaires pour un pays donné.
     * Si aucun timezone n’est trouvé, retourne la liste complète.
     */
    public static function getTimezoneOptions(?string $countryCode = null): array
    {
        $timezones = [];

        if ($countryCode) {
            $timezones = self::getTimezonesForCountry($countryCode);
        }

        // Fallback : liste complète si aucun timezone pour ce pays
        if (empty($timezones)) {
            $timezones = DateTimeZone::listIdentifiers();
        }

        $localizedNames = Timezones::getNames();
        $results = [];

        foreach ($timezones as $tz) {
            $label = $localizedNames[$tz] ?? $tz;
            $abbreviation = now($tz)->format('T');
            $localTime = self::getLocalTime($tz);

            $results[$tz] = "{$label} ({$abbreviation}) {$localTime}";
        }

        return $results;
    }

    /**
     * Retourne l’heure locale actuelle dans un fuseau donné,
     * formatée selon les préférences de l’entreprise.
     */
    public static function getLocalTime(string $timezone): string
    {
        $localization = null;

        // Vérifier si l'utilisateur est connecté et a une entreprise courante
        if (auth()->check() && auth()->user()->currentCompany) {
            $localization = Localization::first();
        }

        $format = $localization?->time_format->value ?? TimeFormat::DEFAULT;

        return now($timezone)->format($format);
    }

    /**
     * Retourne les fuseaux horaires associés à un pays donné.
     */
    public static function getTimezonesForCountry(string $countryCode): array
    {
        return DateTimeZone::listIdentifiers(DateTimeZone::PER_COUNTRY, strtoupper($countryCode));
    }
}
