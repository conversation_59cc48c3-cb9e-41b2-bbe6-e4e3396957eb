import preset from '../../../../vendor/filament/filament/tailwind.config.preset'

export default {
    presets: [preset],
    content: [
        "./app/Filament/Company/**/*.php",
        "./resources/views/filament/company/**/*.blade.php",
        "./vendor/filament/**/*.blade.php",
        "./resources/views/livewire/company/**/*.blade.php",
        "./resources/views/components/**/*.blade.php",
        "./resources/views/vendor/**/*.blade.php",
        "./vendor/andrewdwallo/filament-companies/resources/views/**/*.blade.php",
        "./vendor/andrewdwallo/filament-selectify/resources/views/**/*.blade.php",
        "./vendor/awcodes/filament-table-repeater/resources/**/*.blade.php",
        "./vendor/jaocero/radio-deck/resources/views/**/*.blade.php",
        "./vendor/codewithdennis/filament-simple-alert/resources/**/*.blade.php",
    ],

    theme: {
        extend: {
            colors: {
                //white: "#F3F4F6",
                platinum: "#E8E9EB",
            },
            maxWidth: {
                "8xl": "88rem",
            },
            transitionTimingFunction: {
                "ease-smooth": "cubic-bezier(0.08, 0.52, 0.52, 1)",
            },
        },
    },
};
