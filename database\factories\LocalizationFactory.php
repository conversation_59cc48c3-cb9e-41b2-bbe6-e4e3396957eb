<?php

namespace Database\Factories;

use App\Models\Localization;
use App\Models\Company;
use App\Models\User;
use App\Enums\DateFormat;
use App\Enums\TimeFormat;
use App\Enums\WeekStart;
use App\Enums\NumberFormat;
use Illuminate\Database\Eloquent\Factories\Factory;
use Symfony\Component\Intl\Countries;
use Symfony\Component\Intl\Timezones;

/**
 * @extends Factory<Localization>
 */
class LocalizationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Localization::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Obtenir une liste de configurations régionales populaires
        $localeConfigs = $this->getPopularLocaleConfigs();
        $config = $this->faker->randomElement($localeConfigs);

        return [
            'company_id' => Company::factory(),
            'language' => $config['language'],
            'timezone' => $config['timezone'],
            'date_format' => $config['date_format'],
            'time_format' => $config['time_format'],
            'fiscal_year_end_month' => $this->faker->numberBetween(1, 12),
            'fiscal_year_end_day' => $this->faker->numberBetween(1, 28), // Éviter les problèmes de février
            'week_start' => $config['week_start'],
            'number_format' => $config['number_format'],
            'percent_first' => $config['percent_first'],
            'created_by' => User::factory(),
            'updated_by' => User::factory(),
        ];
    }

    /**
     * Retourne les configurations de locale populaires basées sur Intl
     */
    private function getPopularLocaleConfigs(): array
    {
        return [
            // France
            [
                'language' => 'fr',
                'timezone' => 'Europe/Paris',
                'date_format' => DateFormat::DMY_SLASH,
                'time_format' => TimeFormat::H24,
                'week_start' => WeekStart::Monday,
                'number_format' => NumberFormat::SpaceComma,
                'percent_first' => false,
            ],
            // États-Unis
            [
                'language' => 'en',
                'timezone' => 'America/New_York',
                'date_format' => DateFormat::MDY_SLASH,
                'time_format' => TimeFormat::H12_CAP,
                'week_start' => WeekStart::Sunday,
                'number_format' => NumberFormat::CommaDot,
                'percent_first' => false,
            ],
            // Royaume-Uni
            [
                'language' => 'en',
                'timezone' => 'Europe/London',
                'date_format' => DateFormat::DMY_SLASH,
                'time_format' => TimeFormat::H24,
                'week_start' => WeekStart::Monday,
                'number_format' => NumberFormat::CommaDot,
                'percent_first' => false,
            ],
            // Allemagne
            [
                'language' => 'de',
                'timezone' => 'Europe/Berlin',
                'date_format' => DateFormat::DMY_DOT,
                'time_format' => TimeFormat::H24,
                'week_start' => WeekStart::Monday,
                'number_format' => NumberFormat::DotComma,
                'percent_first' => false,
            ],
            // Canada (anglais)
            [
                'language' => 'en',
                'timezone' => 'America/Toronto',
                'date_format' => DateFormat::DMY_SLASH,
                'time_format' => TimeFormat::H12_CAP,
                'week_start' => WeekStart::Sunday,
                'number_format' => NumberFormat::CommaDot,
                'percent_first' => false,
            ],
            // Canada (français)
            [
                'language' => 'fr',
                'timezone' => 'America/Montreal',
                'date_format' => DateFormat::YMD_DASH,
                'time_format' => TimeFormat::H24,
                'week_start' => WeekStart::Sunday,
                'number_format' => NumberFormat::SpaceComma,
                'percent_first' => false,
            ],
            // Espagne
            [
                'language' => 'es',
                'timezone' => 'Europe/Madrid',
                'date_format' => DateFormat::DMY_SLASH,
                'time_format' => TimeFormat::H24,
                'week_start' => WeekStart::Monday,
                'number_format' => NumberFormat::DotComma,
                'percent_first' => false,
            ],
            // Italie
            [
                'language' => 'it',
                'timezone' => 'Europe/Rome',
                'date_format' => DateFormat::DMY_SLASH,
                'time_format' => TimeFormat::H24,
                'week_start' => WeekStart::Monday,
                'number_format' => NumberFormat::DotComma,
                'percent_first' => false,
            ],
            // Japon
            [
                'language' => 'ja',
                'timezone' => 'Asia/Tokyo',
                'date_format' => DateFormat::YMD_SLASH,
                'time_format' => TimeFormat::H24,
                'week_start' => WeekStart::Sunday,
                'number_format' => NumberFormat::CommaDot,
                'percent_first' => false,
            ],
            // Australie
            [
                'language' => 'en',
                'timezone' => 'Australia/Sydney',
                'date_format' => DateFormat::DMY_SLASH,
                'time_format' => TimeFormat::H12_CAP,
                'week_start' => WeekStart::Monday,
                'number_format' => NumberFormat::CommaDot,
                'percent_first' => false,
            ],
            // Côte d'Ivoire
            [
                'language' => 'fr',
                'timezone' => 'Africa/Abidjan',
                'date_format' => DateFormat::DMY_SLASH,
                'time_format' => TimeFormat::H24,
                'week_start' => WeekStart::Monday,
                'number_format' => NumberFormat::SpaceComma,
                'percent_first' => false,
            ],
        ];
    }

    /**
     * Crée une localisation pour un pays spécifique en utilisant Intl
     */
    public function forCountry(string $countryCode): static
    {
        return $this->state(function (array $attributes) use ($countryCode) {
            $config = $this->getConfigForCountry($countryCode);
            $timezones = $this->getTimezonesForCountry($countryCode);
            
            if (!empty($timezones)) {
                $config['timezone'] = $this->faker->randomElement($timezones);
            }

            return $config;
        });
    }

    /**
     * Obtient les time zones pour un pays spécifique
     */
    private function getTimezonesForCountry(string $countryCode): array
    {
        try {
            return \DateTimeZone::listIdentifiers(\DateTimeZone::PER_COUNTRY, strtoupper($countryCode));
        } catch (\Exception $e) {
            return ['UTC'];
        }
    }

    /**
     * Retourne la configuration par défaut pour un pays
     */
    private function getConfigForCountry(string $countryCode): array
    {
        $configs = $this->getPopularLocaleConfigs();
        
        // Mapping des codes pays vers les configurations
        $countryMapping = [
            'FR' => 0, // France
            'US' => 1, // États-Unis
            'GB' => 2, // Royaume-Uni
            'DE' => 3, // Allemagne
            'CA' => 4, // Canada (anglais)
            'ES' => 6, // Espagne
            'IT' => 7, // Italie
            'JP' => 8, // Japon
            'AU' => 9, // Australie
            'CI' => 10, // Côte d'Ivoire
        ];

        $index = $countryMapping[$countryCode] ?? 0;
        return $configs[$index];
    }

    /**
     * Crée une localisation avec un timezone spécifique
     */
    public function withTimezone(string $timezone): static
    {
        return $this->state(function (array $attributes) use ($timezone) {
            // Valider que le timezone existe
            $timezoneNames = Timezones::getNames();
            if (!isset($timezoneNames[$timezone])) {
                throw new \InvalidArgumentException("Timezone invalide: {$timezone}");
            }

            return [
                'timezone' => $timezone,
            ];
        });
    }

    /**
     * Crée une localisation française
     */
    public function french(): static
    {
        return $this->forCountry('FR');
    }

    /**
     * Crée une localisation américaine
     */
    public function american(): static
    {
        return $this->forCountry('US');
    }

    /**
     * Crée une localisation britannique
     */
    public function british(): static
    {
        return $this->forCountry('GB');
    }

    /**
     * Crée une localisation allemande
     */
    public function german(): static
    {
        return $this->forCountry('DE');
    }
}
