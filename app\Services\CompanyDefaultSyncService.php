<?php

namespace App\Services;

use App\Models\Company;
use App\Models\CompanyDefault;
use App\Models\CompanyProfile;
use App\Models\Localization;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CompanyDefaultSyncService
{
    /**
     * Synchronise CompanyDefault avec les données d'une entreprise
     */
    public function syncFromCompany(Company $company): void
    {
        DB::transaction(function () use ($company) {
            $this->updateOrCreateCompanyDefault($company);
        });
    }

    /**
     * Synchronise CompanyDefault avec les données d'un profil d'entreprise
     */
    public function syncFromCompanyProfile(CompanyProfile $profile): void
    {
        DB::transaction(function () use ($profile) {
            $company = $profile->company;
            if ($company) {
                $this->updateOrCreateCompanyDefault($company);
            }
        });
    }

    /**
     * Synchronise CompanyDefault avec les données d'une localisation
     */
    public function syncFromLocalization(Localization $localization): void
    {
        DB::transaction(function () use ($localization) {
            $company = $localization->company;
            if ($company) {
                $this->updateOrCreateCompanyDefault($company);
            }
        });
    }



    /**
     * Met à jour ou crée l'enregistrement CompanyDefault (UN SEUL par entreprise)
     */
    private function updateOrCreateCompanyDefault(Company $company): void
    {
        // Charger les relations nécessaires
        $company->load(['profile', 'locale']);

        $profile = $company->profile;
        $locale = $company->locale;
        $owner = $company->owner;

        if (!$owner) {
            Log::warning("Aucun propriétaire trouvé pour l'entreprise {$company->id}");
            return;
        }

        // Vérifier s'il existe déjà un CompanyDefault pour cette entreprise
        $existingCount = CompanyDefault::where('company_id', $company->id)->count();

        if ($existingCount > 1) {
            Log::warning("Plusieurs CompanyDefault trouvés pour l'entreprise {$company->id}. Nettoyage en cours...");
            $this->cleanupDuplicateCompanyDefaults($company->id);
        }

        // Préparer les données à synchroniser
        $updateData = [
            'currency_code' => $profile?->currency_code ?? 'XOF',
            'language' => $locale?->language ?? 'fr',
            'timezone' => $locale?->timezone ?? 'UTC',
            'date_format' => $locale?->date_format?->value ?? 'd/m/Y',
            'week_start' => $locale?->week_start?->value ?? 1,
            'updated_by' => $owner->id,
        ];

        // Mettre à jour ou créer l'enregistrement UNIQUE
        $companyDefault = CompanyDefault::updateOrCreate(
            ['company_id' => $company->id], // Condition de recherche
            array_merge($updateData, [
                'created_by' => $owner->id, // Seulement lors de la création
            ])
        );

        Log::info("CompanyDefault synchronisé pour l'entreprise {$company->id}", [
            'company_name' => $company->name,
            'currency_code' => $updateData['currency_code'],
            'language' => $updateData['language'],
            'timezone' => $updateData['timezone'],
            'action' => $companyDefault->wasRecentlyCreated ? 'created' : 'updated',
        ]);

        // Invalider le cache des paramètres
        $this->invalidateCache($company->id);
    }

    /**
     * Nettoie les doublons de CompanyDefault pour une entreprise
     */
    private function cleanupDuplicateCompanyDefaults(int $companyId): void
    {
        $companyDefaults = CompanyDefault::where('company_id', $companyId)
            ->orderBy('updated_at', 'desc')
            ->get();

        if ($companyDefaults->count() <= 1) {
            return;
        }

        // Garder le plus récent, supprimer les autres
        $keepRecord = $companyDefaults->first();
        $duplicates = $companyDefaults->skip(1);

        foreach ($duplicates as $duplicate) {
            $duplicate->delete();
            Log::info("Doublon CompanyDefault supprimé (ID: {$duplicate->id}) pour l'entreprise {$companyId}");
        }

        Log::info("Nettoyage terminé pour l'entreprise {$companyId}. Enregistrement conservé: {$keepRecord->id}");
    }

    /**
     * Invalide le cache des paramètres de l'entreprise
     */
    private function invalidateCache(int $companyId): void
    {
        try {
            CompanySettingsService::invalidateSettings($companyId);
            Log::info("Cache des paramètres invalidé pour l'entreprise {$companyId}");
        } catch (\Exception $e) {
            Log::warning("Erreur lors de l'invalidation du cache: " . $e->getMessage());
        }
    }

    /**
     * Synchronise tous les CompanyDefault existants
     */
    public function syncAllCompanyDefaults(): void
    {
        $companies = Company::with(['profile', 'locale', 'owner'])->get();

        foreach ($companies as $company) {
            try {
                $this->updateOrCreateCompanyDefault($company);
            } catch (\Exception $e) {
                Log::error("Erreur lors de la synchronisation de l'entreprise {$company->id}: " . $e->getMessage());
            }
        }

        Log::info("Synchronisation de tous les CompanyDefault terminée");
    }
}
