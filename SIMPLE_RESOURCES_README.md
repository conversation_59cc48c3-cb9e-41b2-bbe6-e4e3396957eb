# Simple Resources - Documentation

## Vue d'ensemble

Les nouveaux resources `SimpleInvoiceResource` et `SimpleEstimateResource` ont été créés pour offrir une interface simplifiée de création de factures et devis. Ces resources utilisent la même architecture que les resources existants mais avec une approche plus simple où l'utilisateur saisit les informations et le système effectue automatiquement les calculs.

## Caractéristiques principales

### 1. Interface simplifiée
- Formulaire épuré avec les champs essentiels
- Calculs automatiques en temps réel
- Interface utilisateur intuitive

### 2. Calculs automatiques
- **Sous-total** : Calculé automatiquement à partir de (quantité × prix unitaire) pour chaque ligne
- **Taxes** : Calculées en pourcentage du sous-total
- **Total** : Sous-total + taxes
- **Totaux par ligne** : Affichage en temps réel lors de la saisie

### 3. Architecture cohérente
- Utilise les mêmes modèles (`Invoice` et `Estimate`)
- Respecte la structure existante de la base de données
- Compatible avec les resources existants

## Structure des fichiers

```
app/Filament/Company/Resources/
├── SimpleInvoiceResource.php
├── SimpleEstimateResource.php
├── SimpleInvoiceResource/
│   └── Pages/
│       ├── ListSimpleInvoices.php
│       ├── CreateSimpleInvoice.php
│       ├── ViewSimpleInvoice.php
│       └── EditSimpleInvoice.php
└── SimpleEstimateResource/
    └── Pages/
        ├── ListSimpleEstimates.php
        ├── CreateSimpleEstimate.php
        ├── ViewSimpleEstimate.php
        └── EditSimpleEstimate.php
```

## Fonctionnalités

### SimpleInvoiceResource
- **Navigation** : Groupe "Simple Documents"
- **Champs principaux** :
  - Client (obligatoire)
  - Devise (par défaut : devise de l'entreprise)
  - Numéro de facture (généré automatiquement)
  - Numéro de commande
  - Date de facture
  - Date d'échéance
  - Lignes d'articles (répéteur)
  - Taux de taxe (%)
  - Notes

### SimpleEstimateResource
- **Navigation** : Groupe "Simple Documents"
- **Champs principaux** :
  - Client (obligatoire)
  - Devise (par défaut : devise de l'entreprise)
  - Numéro de devis (généré automatiquement)
  - Numéro de référence
  - Date de devis
  - Date d'expiration
  - Lignes d'articles (répéteur)
  - Taux de taxe (%)
  - Notes

### Lignes d'articles
Chaque ligne contient :
- **Description** : Textarea pour la description de l'article
- **Quantité** : Champ numérique (défaut : 1)
- **Prix unitaire** : Champ numérique avec décimales
- **Total** : Calculé automatiquement et affiché en temps réel

## Logique de calcul

### Lors de la création
1. L'utilisateur saisit les lignes d'articles
2. Le système calcule automatiquement :
   - Sous-total = Σ(quantité × prix unitaire)
   - Total des taxes = sous-total × (taux de taxe / 100)
   - Total = sous-total + total des taxes
3. Les valeurs sont sauvegardées dans la base de données

### Lors de la modification
1. Le système récupère le taux de taxe à partir des données existantes
2. L'utilisateur peut modifier les lignes et le taux de taxe
3. Les calculs sont mis à jour automatiquement
4. Les nouvelles valeurs sont sauvegardées

## Avantages

1. **Simplicité** : Interface épurée pour une saisie rapide
2. **Automatisation** : Calculs automatiques sans erreur
3. **Cohérence** : Utilise la même base de données que les resources complexes
4. **Flexibilité** : Peut coexister avec les resources existants
5. **Évolutivité** : Peut être étendu facilement

## Utilisation

1. Accédez au panel Filament
2. Dans la navigation, trouvez le groupe "Simple Documents"
3. Cliquez sur "Simple Invoices" ou "Simple Estimates"
4. Créez un nouveau document en remplissant les champs
5. Les calculs se font automatiquement lors de la saisie
6. Sauvegardez le document

## Notes techniques

- Les resources utilisent les composants Filament existants (`ClientSelect`, `CurrencySelect`)
- Les calculs sont effectués côté client avec Livewire pour la réactivité
- Les totaux sont recalculés et sauvegardés côté serveur lors de la soumission
- Compatible avec le système de devises existant
- Respecte les permissions et la sécurité du système existant
