<?php

namespace Tests\Feature\Filament;

use App\Filament\Company\Resources\DepartmentResource;
use App\Models\Company;
use App\Models\Department;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class DepartmentResourceTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Company $company;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->company = Company::factory()->create();
        $this->company->users()->attach($this->user, ['role' => 'admin']);
        $this->user->switchCompany($this->company);

        $this->actingAs($this->user);
    }

    public function test_can_render_department_list_page(): void
    {
        $this->get(DepartmentResource::getUrl('index'))
            ->assertSuccessful();
    }

    public function test_can_render_department_create_page(): void
    {
        $this->get(DepartmentResource::getUrl('create'))
            ->assertSuccessful();
    }

    public function test_can_create_department(): void
    {
        $departmentData = [
            'name' => 'Human Resources',
            'description' => 'Manages employee relations and policies',
        ];

        Livewire::test(DepartmentResource\Pages\CreateDepartment::class)
            ->fillForm($departmentData)
            ->call('create')
            ->assertHasNoFormErrors();

        $this->assertDatabaseHas('departments', [
            'name' => 'Human Resources',
            'description' => 'Manages employee relations and policies',
            'company_id' => $this->company->id,
        ]);
    }

    public function test_can_render_department_edit_page(): void
    {
        $department = Department::factory()->create([
            'company_id' => $this->company->id,
        ]);

        $this->get(DepartmentResource::getUrl('edit', ['record' => $department]))
            ->assertSuccessful();
    }

    public function test_can_edit_department(): void
    {
        $department = Department::factory()->create([
            'company_id' => $this->company->id,
            'name' => 'Old Name',
        ]);

        $newData = [
            'name' => 'New Department Name',
            'description' => 'Updated description',
        ];

        Livewire::test(DepartmentResource\Pages\EditDepartment::class, ['record' => $department->getRouteKey()])
            ->fillForm($newData)
            ->call('save')
            ->assertHasNoFormErrors();

        $this->assertDatabaseHas('departments', [
            'id' => $department->id,
            'name' => 'New Department Name',
            'description' => 'Updated description',
        ]);
    }

    public function test_can_render_department_view_page(): void
    {
        $department = Department::factory()->create([
            'company_id' => $this->company->id,
        ]);

        $this->get(DepartmentResource::getUrl('view', ['record' => $department]))
            ->assertSuccessful();
    }

    public function test_can_delete_department(): void
    {
        $department = Department::factory()->create([
            'company_id' => $this->company->id,
        ]);

        Livewire::test(DepartmentResource\Pages\EditDepartment::class, ['record' => $department->getRouteKey()])
            ->callAction('delete');

        $this->assertModelMissing($department);
    }

    public function test_can_create_department_with_parent(): void
    {
        $parentDepartment = Department::factory()->create([
            'company_id' => $this->company->id,
            'name' => 'Parent Department',
        ]);

        $departmentData = [
            'name' => 'Sub Department',
            'parent_id' => $parentDepartment->id,
            'description' => 'A sub-department',
        ];

        Livewire::test(DepartmentResource\Pages\CreateDepartment::class)
            ->fillForm($departmentData)
            ->call('create')
            ->assertHasNoFormErrors();

        $this->assertDatabaseHas('departments', [
            'name' => 'Sub Department',
            'parent_id' => $parentDepartment->id,
            'company_id' => $this->company->id,
        ]);
    }

    public function test_can_assign_manager_to_department(): void
    {
        $manager = User::factory()->create();
        $this->company->users()->attach($manager, ['role' => 'manager']);

        $departmentData = [
            'name' => 'IT Department',
            'manager_id' => $manager->id,
            'description' => 'Information Technology Department',
        ];

        Livewire::test(DepartmentResource\Pages\CreateDepartment::class)
            ->fillForm($departmentData)
            ->call('create')
            ->assertHasNoFormErrors();

        $this->assertDatabaseHas('departments', [
            'name' => 'IT Department',
            'manager_id' => $manager->id,
            'company_id' => $this->company->id,
        ]);
    }
}
