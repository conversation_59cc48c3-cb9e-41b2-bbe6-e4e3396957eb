<?php

namespace App\Listeners;

use App\Events\CompanyConfigured;
use App\Models\Company;
use App\Services\CompanyDefaultSyncService;
use Illuminate\Support\Facades\Log;
use Wallo\FilamentCompanies\Events\CompanyCreated;

class InitializeCompanyConfiguration
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * Synchronise CompanyDefault lors de la création d'une entreprise
     */
    public function handle(CompanyCreated $event): void
    {
        try {
            /** @var Company $company */
            $company = $event->company;

            Log::info("Synchronisation CompanyDefault pour nouvelle entreprise: {$company->name} (ID: {$company->id})");

            $syncService = app(CompanyDefaultSyncService::class);
            $syncService->syncFromCompany($company);

            Log::info("Synchronisation CompanyDefault terminée pour l'entreprise {$company->id}");

            // Déclencher la configuration de l'entreprise
            CompanyConfigured::dispatch($company);

        } catch (\Exception $e) {
            Log::error("Erreur lors de la synchronisation CompanyDefault pour l'entreprise {$event->company->name}: " . $e->getMessage());
        }
    }
}
