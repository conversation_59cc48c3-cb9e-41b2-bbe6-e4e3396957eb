<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Utilisateur administrateur principal
        $admin = User::where('email', '<EMAIL>')->first();
        if (!$admin) {
            $admin = User::factory()->create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]);
        }

        // Utilisateur de test avec entreprise personnelle
        $testUser = User::where('email', '<EMAIL>')->first();
        if (!$testUser) {
            $testUser = User::factory()->withPersonalCompany()->create([
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]);
        }

        // Utilisateurs supplémentaires pour les tests (seulement si pas assez d'utilisateurs)
        $currentUserCount = User::count();
        if ($currentUserCount < 7) {
            $usersToCreate = 7 - $currentUserCount;
            User::factory($usersToCreate)->create([
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]);
        }

        $this->command->info('Utilisateurs créés avec succès !');
        $this->command->info('Admin: <EMAIL> / password');
        $this->command->info('Test: <EMAIL> / password');
    }
}
