<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\Department;
use App\Models\User;
use Illuminate\Database\Seeder;

class DepartmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $companies = Company::all();

        if ($companies->isEmpty()) {
            $this->command->warn('Aucune entreprise trouvée. Exécutez CompanySeeder en premier.');
            return;
        }

        foreach ($companies->take(3) as $company) {
            $this->createDepartmentsForCompany($company);
        }

        // Départements supplémentaires avec factory
        $remainingCompanies = $companies->skip(3);
        $additionalDepartments = [
            'Support Client',
            'Recherche & Développement',
            'Qualité',
            'Logistique',
            'Formation',
            'Sécurité',
            'Administration',
            'Achats',
        ];

        foreach ($remainingCompanies as $index => $company) {
            // Prendre 2 départements différents pour chaque entreprise
            $departmentsForCompany = array_slice($additionalDepartments, ($index * 2) % count($additionalDepartments), 2);

            foreach ($departmentsForCompany as $departmentName) {
                Department::firstOrCreate(
                    [
                        'company_id' => $company->id,
                        'name' => $departmentName,
                    ],
                    [
                        'manager_id' => null,
                        'created_by' => $company->user_id,
                        'updated_by' => $company->user_id,
                    ]
                );
            }
        }

        $this->command->info('Départements créés avec succès !');
    }

    private function createDepartmentsForCompany(Company $company): void
    {
        $departments = [
            'Direction Générale',
            'Ressources Humaines',
            'Comptabilité',
            'Commercial',
            'Technique',
            'Marketing',
        ];

        foreach ($departments as $departmentName) {
            Department::firstOrCreate(
                [
                    'company_id' => $company->id,
                    'name' => $departmentName,
                ],
                [
                    'manager_id' => null, // Sera assigné plus tard
                    'created_by' => $company->user_id,
                    'updated_by' => $company->user_id,
                ]
            );
        }
    }
}
