<?php

namespace App\Filament\Company\Pages;

use App\Models\Country;
use Filament\Forms\Get;
use Filament\Forms\Set;
use App\Enums\WeekStart;
use Filament\Forms\Form;
use Filament\Pages\Page;
use App\Enums\DateFormat;
use App\Enums\TimeFormat;
use App\Enums\NumberFormat;
use App\Utilities\Timezone;
use Filament\Actions\Action;
use Livewire\Attributes\Locked;
use function Filament\authorize;
use Filament\Actions\ActionGroup;
use Filament\Forms\Components\Group;
use Filament\Support\Enums\MaxWidth;
use Filament\Forms\Components\Select;
use Filament\Support\Exceptions\Halt;
use Filament\Forms\Components\Section;
use Illuminate\Database\Eloquent\Model;
use App\Services\CompanySettingsService;
use Filament\Forms\Components\Component;
use Filament\Notifications\Notification;
use Guava\FilamentClusters\Forms\Cluster;
use Illuminate\Contracts\Support\Htmlable;
use App\Filament\Company\Clusters\Settings;
use App\Models\Localization as LocalizationModel;

use Illuminate\Auth\Access\AuthorizationException;

use App\Models\CompanyProfile as CompanyProfileModel;
use Filament\Pages\Concerns\InteractsWithFormActions;

class Localization extends Page
{
    use InteractsWithFormActions;

    protected static ?string $cluster = Settings::class;

    protected static ?string $title = 'Localization';

    //protected static ?string $navigationIcon = 'heroicon-o-map';

    protected static string $view = 'filament.company.pages.localization';

    public ?array $data = [];

    #[Locked]
    public ?LocalizationModel $record = null;

    public function getTitle(): string|Htmlable
    {
        return static::$title;
    }

    public static function getNavigationLabel(): string
    {
        return static::$title;
    }

    public function getMaxContentWidth(): MaxWidth|string|null
    {
        return MaxWidth::ScreenTwoExtraLarge;
    }

    public function mount(): void
    {
        $this->record = LocalizationModel::firstOrNew([
            'company_id' => auth()->user()->current_company_id,
        ]);

        abort_unless(static::canView($this->record), 404);

        $this->fillForm();
    }

    public function fillForm(): void
    {
        $data = $this->record->attributesToArray();

        $this->form->fill($data);
    }

    public function save(): void
    {
        try {
            $data = $this->form->getState();

            $this->handleRecordUpdate($this->record, $data);
        } catch (Halt $exception) {
            return;
        }

        $this->getSavedNotification()->send();
    }

    protected function getSavedNotification(): Notification
    {
        return Notification::make()
            ->success()
            ->title(__('filament-panels::resources/pages/edit-record.notifications.saved.title'));
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                $this->getGeneralSection(),
                $this->getDateAndTimeSection(),
                $this->getFinancialAndFiscalSection(),
            ])
            ->model($this->record)
            ->statePath('data')
            ->operation('edit');
    }

    protected function getGeneralSection(): Component
    {
        return Section::make('General')
            ->schema([
                Select::make('language')
                    ->required()
                    ->label('Language')
                    ->options(Country::getLanguagesByCountryCode())
                    ->disabled(false)
                    ->searchable(),

                Select::make('timezone')
                    ->label('Timezone')
                    ->required()
                    ->options(function () {
                        $countryCode = null;
                        if (auth()->check() && auth()->user()->currentCompany) {
                            $countryCode = CompanyProfileModel::where('company_id', auth()->user()->currentCompany->id)
                                ->first()?->address?->country_code;
                        }
                        return Timezone::getTimezoneOptions($countryCode);
                    })
                    ->searchable()
                    ->helperText('Select a timezone. If none are available for your country, the full list will be shown.'),
            ])->columns();
    }

    protected function getDateAndTimeSection(): Component
    {
        return Section::make('Date & Time')
            ->schema([
                Select::make('date_format')
                    ->required()
                    ->label('Date Format')
                    ->options(DateFormat::class)
                    ->live(),
                Select::make('time_format')
                    ->required()
                    ->label('Time Format')
                    ->options(TimeFormat::class),
                Select::make('week_start')
                    ->required()
                    ->label('Week Start')
                    ->options(WeekStart::class),
            ])->columns();
    }

    protected function getFinancialAndFiscalSection(): Component
    {
        $beforeNumber = ('Before number');
        $afterNumber = ('After number');
        $selectPosition = ('Select position');

        return Section::make('Financial & Fiscal')
            ->schema([
                Select::make('number_format')
                    ->required()
                    ->label('Number Format')
                    ->options(NumberFormat::class),
                Select::make('percent_first')
                    ->required()
                    ->label('Percent position')
                    ->boolean($beforeNumber, $afterNumber, $selectPosition),
                Group::make()
                    ->schema([
                        Cluster::make([
                            Select::make('fiscal_year_end_month')
                                ->required()
                                ->options(array_combine(range(1, 12), array_map(static fn($month) => now()->month($month)->monthName, range(1, 12))))
                                ->afterStateUpdated(static fn(Set $set) => $set('fiscal_year_end_day', null))
                                ->columnSpan(2)
                                ->live(),
                            Select::make('fiscal_year_end_day')
                                ->placeholder('Day')
                                ->required()
                                ->columnSpan(1)
                                ->options(function (Get $get) {
                                    $month = (int) $get('fiscal_year_end_month');

                                    $daysInMonth = now()->month($month)->daysInMonth;

                                    return array_combine(range(1, $daysInMonth), range(1, $daysInMonth));
                                })
                                ->live(),
                        ])
                            ->columns(3)
                            ->columnSpan(2)
                            ->required()
                            ->markAsRequired(false)
                            ->label('Fiscal year end'),
                    ])->columns(3),
            ])->columns();
    }

    protected function handleRecordUpdate(LocalizationModel $record, array $data): LocalizationModel
    {
        $record->fill($data);

        $keysToWatch = [
            'language',
            'timezone',
            'date_format',
            'week_start',
            'time_format',
        ];

        if ($record->isDirty($keysToWatch)) {
            CompanySettingsService::invalidateSettings($record->company_id);
            $this->dispatch('localizationUpdated');
        }

        $record->save();

        return $record;
    }

    /**
     * @return array<Action | ActionGroup>
     */
    protected function getFormActions(): array
    {
        return [
            $this->getSaveFormAction(),
        ];
    }

    protected function getSaveFormAction(): Action
    {
        return Action::make('save')
            ->label(__('filament-panels::resources/pages/edit-record.form.actions.save.label'))
            ->submit('save')
            ->keyBindings(['mod+s']);
    }

    public static function canView(Model $record): bool
    {
        try {
            return authorize('update', $record)->allowed();
        } catch (AuthorizationException $exception) {
            return $exception->toResponse()->allowed();
        }
    }
}
