<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Event;
use App\Listeners\InitializeCompanyConfiguration;
use App\Listeners\ConfigureCompanyDefault;
use App\Listeners\SyncCompanyDefaultOnCompanyUpdate;
use App\Events\CompanyConfigured;
use Wallo\FilamentCompanies\Events\CompanyCreated;
use Wallo\FilamentCompanies\Events\CompanyUpdated;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Enregistrer les listeners d'événements pour les entreprises
        Event::listen(
            CompanyCreated::class,
            InitializeCompanyConfiguration::class
        );

        Event::listen(
            CompanyUpdated::class,
            SyncCompanyDefaultOnCompanyUpdate::class
        );

        Event::listen(
            CompanyConfigured::class,
            ConfigureCompanyDefault::class
        );
    }
}
